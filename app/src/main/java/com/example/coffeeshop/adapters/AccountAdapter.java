package com.example.coffeeshop.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.example.coffeeshop.R;
import com.example.coffeeshop.activities.AccountManagementActivity.Account;

import java.util.ArrayList;
import java.util.List;

public class AccountAdapter extends RecyclerView.Adapter<AccountAdapter.AccountViewHolder> {

    private List<Account> accounts = new ArrayList<>();
    private Context context;
    private OnAccountActionListener listener;

    public interface OnAccountActionListener {
        void onAccountDeleted(Account account, int position);
        void onAccountEdit(Account account);
        void onAccountStatusToggled(Account account, int position);
    }

    public AccountAdapter(Context context, OnAccountActionListener listener) {
        this.context = context;
        this.listener = listener;
    }

    public void updateList(List<Account> newAccounts) {
        accounts.clear();
        if (newAccounts != null) {
            accounts.addAll(newAccounts);
        }
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public AccountViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_account, parent, false);
        return new AccountViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull AccountViewHolder holder, int position) {
        Account account = accounts.get(position);

        // Bind data to views
        holder.tvUsername.setText(account.getUsername());
        holder.tvEmail.setText(account.getEmail());
        holder.tvPhone.setText(account.getPhone());
        holder.chipRole.setText(account.getRole());
        holder.chipStatus.setText(account.getStatus());

        // Set status chip appearance and toggle button text
        if ("Active".equals(account.getStatus())) {
            holder.chipStatus.setChipBackgroundColorResource(android.R.color.holo_green_light);
            holder.chipStatus.setTextColor(context.getResources().getColor(android.R.color.white));
            holder.btnToggleStatus.setText("Deactivate");
            holder.btnToggleStatus.setTextColor(context.getResources().getColor(android.R.color.holo_red_dark));
        } else {
            holder.chipStatus.setChipBackgroundColorResource(android.R.color.darker_gray);
            holder.chipStatus.setTextColor(context.getResources().getColor(android.R.color.white));
            holder.btnToggleStatus.setText("Activate");
            holder.btnToggleStatus.setTextColor(context.getResources().getColor(android.R.color.holo_green_dark));
        }

        // Handle button clicks
        holder.btnEdit.setOnClickListener(v -> {
            if (listener != null) {
                listener.onAccountEdit(account);
            }
        });

        holder.btnToggleStatus.setOnClickListener(v -> {
            // Toggle account status
            String newStatus = "Active".equals(account.getStatus()) ? "Inactive" : "Active";
            account.setStatus(newStatus);

            // Notify listener about status change
            if (listener != null) {
                listener.onAccountStatusToggled(account, position);
            }

            // Update this item's view
            notifyItemChanged(position);
        });

        holder.btnDelete.setOnClickListener(v -> {
            showDeleteConfirmation(account, position);
        });
    }

    @Override
    public int getItemCount() {
        return accounts.size();
    }

    private void showDeleteConfirmation(Account account, int position) {
        new AlertDialog.Builder(context)
                .setTitle("Delete Account")
                .setMessage("Are you sure you want to delete " + account.getUsername() + "'s account?\n\nThis action cannot be undone.")
                .setPositiveButton("Delete", (dialog, which) -> {
                    if (listener != null) {
                        listener.onAccountDeleted(account, position);
                    }
                })
                .setNegativeButton("Cancel", null)
                .show();
    }

    static class AccountViewHolder extends RecyclerView.ViewHolder {
        TextView tvUsername, tvEmail, tvPhone;
        Chip chipRole, chipStatus;
        MaterialButton btnEdit, btnToggleStatus, btnDelete;

        AccountViewHolder(View itemView) {
            super(itemView);

            // Initialize views with correct IDs from item_account.xml
            tvUsername = itemView.findViewById(R.id.tv_user_name);
            tvEmail = itemView.findViewById(R.id.tv_user_email);
            tvPhone = itemView.findViewById(R.id.tv_user_phone);
            chipRole = itemView.findViewById(R.id.chip_user_role);
            chipStatus = itemView.findViewById(R.id.chip_user_status);
            btnEdit = itemView.findViewById(R.id.btn_edit_user);
            btnToggleStatus = itemView.findViewById(R.id.btn_toggle_status);
            btnDelete = itemView.findViewById(R.id.btn_delete_user);
        }
    }
}