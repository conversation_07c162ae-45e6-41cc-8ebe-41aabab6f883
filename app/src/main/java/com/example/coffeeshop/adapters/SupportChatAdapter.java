package com.example.coffeeshop.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.coffeeshop.models.SupportResponse;
import com.example.coffeeshop.R;
import java.util.List;

public class SupportChatAdapter extends RecyclerView.Adapter<SupportChatAdapter.ChatViewHolder> {
    private List<SupportResponse> responses;
    private int currentUserId;

    public SupportChatAdapter(List<SupportResponse> responses, int currentUserId) {
        this.responses = responses;
        this.currentUserId = currentUserId;
    }

    @NonNull
    @Override
    public ChatViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_support_chat, parent, false);
        return new ChatViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ChatViewHolder holder, int position) {
        SupportResponse response = responses.get(position);
        holder.textMessage.setText(response.message);
        holder.textSender.setText(response.responderRole);
        holder.textTime.setText(response.createdAt);
        // Optionally, style differently if current user
        if (response.responderId == currentUserId) {
            holder.textMessage.setBackgroundResource(R.drawable.bg_chat_bubble_self);
        } else {
            holder.textMessage.setBackgroundResource(R.drawable.bg_chat_bubble_other);
        }
    }

    @Override
    public int getItemCount() {
        return responses.size();
    }

    static class ChatViewHolder extends RecyclerView.ViewHolder {
        TextView textMessage, textSender, textTime;
        ChatViewHolder(@NonNull View itemView) {
            super(itemView);
            textMessage = itemView.findViewById(R.id.textChatMessage);
            textSender = itemView.findViewById(R.id.textChatSender);
            textTime = itemView.findViewById(R.id.textChatTime);
        }
    }
}

