package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.User;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationBarView;
import com.google.android.material.textfield.TextInputEditText;

import org.json.JSONObject;

public class ProfileActivity extends AppCompatActivity {

    private TextInputEditText etFullName, etEmail, etPhone;
    private Button btnSaveProfile, btnChangePassword, btnLogout;
    private BottomNavigationView bottomNavigation;
    
    private MockDataManager dataManager;
    private ApiService apiService;
    private User currentUser;
    private int currentUserId = 1; // Default customer

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_profile);
        
        // Get user ID from intent
        currentUserId = getIntent().getIntExtra("userId", 1);
        
        dataManager = MockDataManager.getInstance();
        apiService = new ApiService();
        initViews();
        setupBottomNavigation();
        loadUserProfile();
        setupListeners();
    }

    private void initViews() {
        etFullName = findViewById(R.id.et_full_name);
        etEmail = findViewById(R.id.et_email);
        etPhone = findViewById(R.id.et_phone);
        btnSaveProfile = findViewById(R.id.btn_save_profile);
        btnChangePassword = findViewById(R.id.btn_change_password);
        btnLogout = findViewById(R.id.btn_logout);
        bottomNavigation = findViewById(R.id.bottom_navigation);
    }

    private void loadUserProfile() {
        // Load user profile from API first, then fallback to MockDataManager
        apiService.getUserProfile(currentUserId, new ApiService.UserCallback() {
            @Override
            public void onSuccess(JSONObject userJson) {
                try {
                    // Create user object from API response
                    currentUser = new User(
                        userJson.getInt("id"),
                        userJson.getString("full_name"),
                        userJson.getString("email"),
                        userJson.optString("phone", ""),
                        "", // password hash not returned by API
                        userJson.optInt("role_id", 1),
                        new java.util.Date() // created_at not returned by API, use current date
                    );
                    
                    // Update UI fields
                    etFullName.setText(currentUser.getFullName());
                    etEmail.setText(currentUser.getEmail());
                    etPhone.setText(currentUser.getPhone());
                    
                } catch (Exception e) {
                    e.printStackTrace();
                    loadUserProfileFromMock();
                }
            }

            @Override
            public void onError(String error) {
                loadUserProfileFromMock();
            }
        });
    }
    
    private void loadUserProfileFromMock() {
        currentUser = dataManager.findUserById(currentUserId);
        
        if (currentUser != null) {
            etFullName.setText(currentUser.getFullName());
            etEmail.setText(currentUser.getEmail());
            etPhone.setText(currentUser.getPhone());
        } else {
            Toast.makeText(this, "User not found", Toast.LENGTH_SHORT).show();
            finish();
        }
    }

    private void setupListeners() {
        findViewById(R.id.btn_back).setOnClickListener(v -> finish());
        
        btnSaveProfile.setOnClickListener(v -> saveProfile());
        btnChangePassword.setOnClickListener(v -> changePassword());
        btnLogout.setOnClickListener(v -> logout());
    }

    private void saveProfile() {
        String fullName = etFullName.getText().toString().trim();
        String email = etEmail.getText().toString().trim();
        String phone = etPhone.getText().toString().trim();
        
        // Validate inputs
        if (fullName.isEmpty()) {
            etFullName.setError("Full name is required");
            etFullName.requestFocus();
            return;
        }
        
        if (email.isEmpty()) {
            etEmail.setError("Email is required");
            etEmail.requestFocus();
            return;
        }
        
        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            etEmail.setError("Invalid email format");
            etEmail.requestFocus();
            return;
        }
        
        if (phone.isEmpty()) {
            etPhone.setError("Phone number is required");
            etPhone.requestFocus();
            return;
        }
        
        // Clear any existing errors
        etFullName.setError(null);
        etEmail.setError(null);
        etPhone.setError(null);
        
        // Disable save button to prevent multiple submissions
        btnSaveProfile.setEnabled(false);
        btnSaveProfile.setText("Saving...");
        
        // Update profile via API first, then fallback to MockDataManager
        apiService.updateUserProfile(currentUserId, fullName, email, phone, new ApiService.ProfileUpdateCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                // Update local user object
                if (currentUser != null) {
                    currentUser.setFullName(fullName);
                    currentUser.setEmail(email);
                    currentUser.setPhone(phone);
                }
                
                Toast.makeText(ProfileActivity.this, "Profile updated successfully!", Toast.LENGTH_SHORT).show();
                
                // Re-enable save button
                btnSaveProfile.setEnabled(true);
                btnSaveProfile.setText("Save Profile");
            }

            @Override
            public void onError(String error) {
                // Fallback to MockDataManager
                saveProfileWithMock(fullName, email, phone);
            }
        });
    }
    
    private void saveProfileWithMock(String fullName, String email, String phone) {
        try {
            // Check if email is already taken by another user
            User existingUser = dataManager.findUserByEmail(email);
            if (existingUser != null && existingUser.getUserId() != currentUserId) {
                etEmail.setError("Email is already taken");
                etEmail.requestFocus();
                btnSaveProfile.setEnabled(true);
                btnSaveProfile.setText("Save Profile");
                return;
            }
            
            // Update user profile
            if (currentUser != null) {
                currentUser.setFullName(fullName);
                currentUser.setEmail(email);
                currentUser.setPhone(phone);
                
                boolean success = dataManager.updateUser(currentUser);
                
                if (success) {
                    Toast.makeText(this, "Profile updated successfully (offline)!", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(this, "Failed to update profile", Toast.LENGTH_SHORT).show();
                }
            }
        } catch (Exception e) {
            Toast.makeText(this, "Error updating profile: " + e.getMessage(), Toast.LENGTH_LONG).show();
        } finally {
            // Re-enable save button
            btnSaveProfile.setEnabled(true);
            btnSaveProfile.setText("Save Profile");
        }
    }

    private void changePassword() {
        // Create a dialog with input fields for current and new password
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle("Change Password");
        
        // Create layout for the dialog
        android.widget.LinearLayout layout = new android.widget.LinearLayout(this);
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(50, 40, 50, 10);
        
        // Current password input
        com.google.android.material.textfield.TextInputLayout currentPasswordLayout = new com.google.android.material.textfield.TextInputLayout(this);
        currentPasswordLayout.setHint("Current Password");
        com.google.android.material.textfield.TextInputEditText etCurrentPassword = new com.google.android.material.textfield.TextInputEditText(this);
        etCurrentPassword.setInputType(android.text.InputType.TYPE_CLASS_TEXT | android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD);
        currentPasswordLayout.addView(etCurrentPassword);
        layout.addView(currentPasswordLayout);
        
        // New password input
        com.google.android.material.textfield.TextInputLayout newPasswordLayout = new com.google.android.material.textfield.TextInputLayout(this);
        newPasswordLayout.setHint("New Password");
        com.google.android.material.textfield.TextInputEditText etNewPassword = new com.google.android.material.textfield.TextInputEditText(this);
        etNewPassword.setInputType(android.text.InputType.TYPE_CLASS_TEXT | android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD);
        newPasswordLayout.addView(etNewPassword);
        layout.addView(newPasswordLayout);
        
        // Confirm password input
        com.google.android.material.textfield.TextInputLayout confirmPasswordLayout = new com.google.android.material.textfield.TextInputLayout(this);
        confirmPasswordLayout.setHint("Confirm New Password");
        com.google.android.material.textfield.TextInputEditText etConfirmPassword = new com.google.android.material.textfield.TextInputEditText(this);
        etConfirmPassword.setInputType(android.text.InputType.TYPE_CLASS_TEXT | android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD);
        confirmPasswordLayout.addView(etConfirmPassword);
        layout.addView(confirmPasswordLayout);
        
        builder.setView(layout);
        
        builder.setPositiveButton("Change Password", (dialog, which) -> {
            String currentPassword = etCurrentPassword.getText().toString().trim();
            String newPassword = etNewPassword.getText().toString().trim();
            String confirmPassword = etConfirmPassword.getText().toString().trim();
            
            // Validate inputs
            if (currentPassword.isEmpty()) {
                Toast.makeText(this, "Current password is required", Toast.LENGTH_SHORT).show();
                return;
            }
            
            if (newPassword.isEmpty()) {
                Toast.makeText(this, "New password is required", Toast.LENGTH_SHORT).show();
                return;
            }
            
            if (newPassword.length() < 6) {
                Toast.makeText(this, "New password must be at least 6 characters", Toast.LENGTH_SHORT).show();
                return;
            }
            
            if (!newPassword.equals(confirmPassword)) {
                Toast.makeText(this, "New passwords do not match", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // Change password via API
            apiService.changePassword(currentUserId, currentPassword, newPassword, new ApiService.ProfileUpdateCallback() {
                @Override
                public void onSuccess(JSONObject response) {
                    Toast.makeText(ProfileActivity.this, "Password changed successfully!", Toast.LENGTH_SHORT).show();
                }

                @Override
                public void onError(String error) {
                    Toast.makeText(ProfileActivity.this, "Failed to change password: " + error, Toast.LENGTH_LONG).show();
                }
            });
        });
        
        builder.setNegativeButton("Cancel", null);
        builder.show();
    }

    private void logout() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Logout")
                .setMessage("Are you sure you want to logout?")
                .setPositiveButton("Yes", (dialog, which) -> {
                    // Clear any session data here if needed
                    Toast.makeText(this, "Logged out successfully", Toast.LENGTH_SHORT).show();
                    
                    // Navigate to login screen
                    Intent intent = new Intent(this, LoginActivity.class);
                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                    startActivity(intent);
                    finish();
                })
                .setNegativeButton("No", null)
                .show();
    }
    
    private void setupBottomNavigation() {
        if (bottomNavigation == null) return;

        bottomNavigation.setSelectedItemId(R.id.nav_profile);
        bottomNavigation.setOnItemSelectedListener(new NavigationBarView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_home) {
                    // Navigate to home
                    Intent intent = new Intent(ProfileActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_menu) {
                    // Navigate to menu (home)
                    Intent intent = new Intent(ProfileActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    // Navigate to Order History
                    Intent intent = new Intent(ProfileActivity.this, OrderHistoryActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_favorites) {
                    // Show a simple message since favorites isn't implemented
                    Toast.makeText(ProfileActivity.this, "Favorites - Coming Soon!", Toast.LENGTH_SHORT).show();
                    return true;
                } else if (itemId == R.id.nav_profile) {
                    // Already on profile screen
                    return true;
                }
                return false;
            }
        });
    }
}
