package com.example.coffeeshop.models;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class Order {
    private int orderId;
    private int customerId;
    private String customerName;
    private String status; // pending, preparing, finished
    private BigDecimal totalPrice;
    private Integer discountId;
    private Date createdAt;
    private List<OrderItem> orderItems;
    private String itemsSummary; // For API orders that don't have full OrderItem objects

    // Constructor
    public Order() {
        this.createdAt = new Date();
        this.status = "pending";
    }

    public Order(int orderId, int customerId, String customerName, String status,
                 BigDecimal totalPrice, Integer discountId, Date createdAt) {
        this.orderId = orderId;
        this.customerId = customerId;
        this.customerName = customerName;
        this.status = status;
        this.totalPrice = totalPrice;
        this.discountId = discountId;
        this.createdAt = createdAt;
    }

    // Getters and Setters
    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Integer getDiscountId() {
        return discountId;
    }

    public void setDiscountId(Integer discountId) {
        this.discountId = discountId;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public List<OrderItem> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<OrderItem> orderItems) {
        this.orderItems = orderItems;
    }

    public String getItemsSummary() {
        return itemsSummary;
    }

    public void setItemsSummary(String itemsSummary) {
        this.itemsSummary = itemsSummary;
    }

    // Utility methods
    public String getFormattedCreatedAt() {
        SimpleDateFormat formatter = new SimpleDateFormat("MMM dd, HH:mm", Locale.getDefault());
        return formatter.format(createdAt);
    }

    public String getFormattedTotalPrice() {
        return "$" + totalPrice.toString();
    }

    public int getItemCount() {
        return orderItems != null ? orderItems.size() : 0;
    }

    public String getStatusColor() {
        switch (status.toLowerCase()) {
            case "pending":
                return "#FF9800"; // Orange
            case "preparing":
                return "#2196F3"; // Blue
            case "ready":
                return "#4CAF50"; // Green
            case "completed":
                return "#8BC34A"; // Light Green
            case "cancelled":
                return "#F44336"; // Red
            default:
                return "#757575"; // Grey
        }
    }

    public boolean canBeUpdated() {
        return !"finished".equals(status);
    }

    // Additional methods for customer screens
    public String getDeliveryAddress() {
        return "123 Main Street"; // Mock delivery address
    }

    public int getUserId() {
        return customerId; // Alias for compatibility
    }

    // Additional methods for barista screens
    public int getId() {
        return orderId; // Alias for compatibility with barista screens
    }

    public String getSpecialInstructions() {
        return "No special instructions"; // Mock special instructions
    }

    public List<OrderItem> getItems() {
        return orderItems; // Alias for compatibility
    }
}
