<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="Account Management"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_revert"
            app:navigationIconTint="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Search and Filter Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                style="@style/Widget.CoffeeShop.ListItem">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Search Bar -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_search"
                        style="@style/Widget.CoffeeShop.TextInputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Search by name, email, or phone"
                        android:layout_marginBottom="16dp"
                        app:startIconDrawable="@android:drawable/ic_menu_search"
                        app:endIconMode="clear_text">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_search"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Filter and Sort Row -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <!-- Sort Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_sort"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:text="Sort By"
                            app:icon="@android:drawable/ic_menu_sort_by_size"
                            app:iconGravity="textStart"
                            app:strokeColor="@color/primary" />

                        <!-- Add User Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_add_user"
                            style="@style/Widget.CoffeeShop.Button"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:text="Add"
                            app:icon="@android:drawable/ic_menu_add"
                            app:iconGravity="textStart" />

                    </LinearLayout>

                    <!-- Role Filter Chips -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Filter by Role:"
                        android:textAppearance="?attr/textAppearanceCaption"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chip_group_roles"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        app:singleSelection="false">

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_all_roles"
                            style="@style/Widget.MaterialComponents.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="All Roles"
                            android:checked="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_barista"
                            style="@style/Widget.MaterialComponents.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Barista" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_manager"
                            style="@style/Widget.MaterialComponents.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Manager" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_shipper"
                            style="@style/Widget.MaterialComponents.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Shipper" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_support"
                            style="@style/Widget.MaterialComponents.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Support" />

                    </com.google.android.material.chip.ChipGroup>

                    <!-- Status Filter Chips -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Filter by Status:"
                        android:textAppearance="?attr/textAppearanceCaption"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chip_group_status"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:singleSelection="false">

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_all_status"
                            style="@style/Widget.MaterialComponents.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="All Status"
                            android:checked="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_active"
                            style="@style/Widget.MaterialComponents.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Active" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_inactive"
                            style="@style/Widget.MaterialComponents.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Inactive" />

                    </com.google.android.material.chip.ChipGroup>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- User Count and Export Section -->
<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:orientation="horizontal"-->
<!--                android:layout_marginBottom="16dp"-->
<!--                android:gravity="center_vertical">-->

<!--                <TextView-->
<!--                    android:id="@+id/tv_user_count"-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_weight="1"-->
<!--                    android:text="0 users"-->
<!--                    android:textAppearance="?attr/textAppearanceSubtitle2"-->
<!--                    android:textColor="@color/primary" />-->

<!--                <com.google.android.material.button.MaterialButton-->
<!--                    android:id="@+id/btn_export_data"-->
<!--                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="Export"-->
<!--                    app:icon="@android:drawable/ic_menu_save"-->
<!--                    app:iconGravity="textStart"-->
<!--                    app:strokeColor="@color/primary" />-->

<!--            </LinearLayout>-->

            <!-- Users List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_users"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_account" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layout_empty_state"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@android:drawable/ic_menu_search"
                    android:alpha="0.5"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No users found"
                    android:textAppearance="?attr/textAppearanceSubtitle1"
                    android:textColor="@android:color/darker_gray"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Try adjusting your search or filters"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="@android:color/darker_gray" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>