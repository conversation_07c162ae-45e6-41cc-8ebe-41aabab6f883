package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.ProductAdapter;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.CartItem;
import com.example.coffeeshop.models.Order;
import com.example.coffeeshop.models.Product;
import com.example.coffeeshop.models.User;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.navigation.NavigationBarView;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.textfield.TextInputEditText;

import java.util.ArrayList;
import java.util.List;

public class CustomerDashboardActivity extends AppCompatActivity implements ProductAdapter.OnAddToCartClickListener {

    private TextView tvWelcome, tvCartBadge, tvProductsCount, tvCurrentOrderId, tvCurrentOrderStatus;
    private Button btnCart, btnSort, btnTrackOrder;
    private MaterialCardView cardCurrentOrder;
    private TabLayout tabCategories;
    private RecyclerView rvProducts;
    private BottomNavigationView bottomNavigation;
    private SwipeRefreshLayout swipeRefresh;
    private TextInputEditText etSearch;

    private MockDataManager dataManager;
    private ProductAdapter productAdapter;
    private List<Product> allProducts;
    private List<Product> filteredProducts;
    private String currentCategory = "All";
    private int currentUserId = 1; // Default to test customer    @Override

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            setContentView(R.layout.activity_customer_dashboard_fixed);
            dataManager = MockDataManager.getInstance();

            initViews();
            setupProductList();
            setupCategoryTabs();
            setupBottomNavigation();
            updateCartBadge();
            updateCurrentOrderDisplay();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Error starting dashboard: " + e.getMessage(), Toast.LENGTH_LONG).show();
            finish(); // Close the activity instead of showing broken view
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateCartBadge();
        updateCurrentOrderDisplay();
    }

    private void initViews() {
        try {
            tvWelcome = findViewById(R.id.tv_welcome);
            tvCartBadge = findViewById(R.id.tv_cart_badge);
            tvProductsCount = findViewById(R.id.tv_products_count);
            btnCart = findViewById(R.id.btn_cart);
            btnSort = findViewById(R.id.btn_sort);
            tabCategories = findViewById(R.id.tab_categories);
            rvProducts = findViewById(R.id.rv_products);
            bottomNavigation = findViewById(R.id.bottom_navigation);
            swipeRefresh = findViewById(R.id.swipe_refresh);
            etSearch = findViewById(R.id.et_search);
            
            // Current order views
            cardCurrentOrder = findViewById(R.id.card_current_order);
            tvCurrentOrderId = findViewById(R.id.tv_current_order_id);
            tvCurrentOrderStatus = findViewById(R.id.tv_current_order_status);
            btnTrackOrder = findViewById(R.id.btn_track_order);

            // Set welcome message
            User currentUser = dataManager.findUserByEmail("<EMAIL>");
            if (currentUser != null && tvWelcome != null) {
                tvWelcome.setText("Welcome back, " + currentUser.getFullName().split(" ")[0] + "!");
                currentUserId = currentUser.getUserId();
            }

            if (btnCart != null) {
                btnCart.setOnClickListener(v -> openCart());
            }
            if (btnSort != null) {
                btnSort.setOnClickListener(v -> showSortOptions());
            }
            if (btnTrackOrder != null) {
                btnTrackOrder.setOnClickListener(v -> openOrderTracking());
            }
            setupSearchFunctionality();
            setupSwipeRefresh();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Error initializing views: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void initViewsSimple() {
        try {
            tvWelcome = findViewById(R.id.tv_welcome);
            tvCartBadge = findViewById(R.id.tv_cart_badge);
            btnCart = findViewById(R.id.btn_cart);
            rvProducts = findViewById(R.id.rv_products);

            // Set welcome message
            User currentUser = dataManager.findUserByEmail("<EMAIL>");
            if (currentUser != null && tvWelcome != null) {
                tvWelcome.setText("Welcome, " + currentUser.getFullName().split(" ")[0] + "!");
                currentUserId = currentUser.getUserId();
            }

            if (btnCart != null) {
                btnCart.setOnClickListener(v -> openCart());
            }
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Error initializing views: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void setupProductList() {
        if (rvProducts == null) {
            Toast.makeText(this, "Product list not available", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            allProducts = dataManager.getAllProducts();
            filteredProducts = new ArrayList<>(allProducts);
            productAdapter = new ProductAdapter(this, filteredProducts);
            productAdapter.setOnAddToCartClickListener(this);

            rvProducts.setLayoutManager(new LinearLayoutManager(this));
            rvProducts.setAdapter(productAdapter);
            updateProductsCount();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Error loading products: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void setupProductListSimple() {
        if (rvProducts == null) {
            Toast.makeText(this, "Product list not available", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            allProducts = dataManager.getAllProducts();
            filteredProducts = new ArrayList<>(allProducts);
            productAdapter = new ProductAdapter(this, filteredProducts);
            productAdapter.setOnAddToCartClickListener(this);

            rvProducts.setLayoutManager(new LinearLayoutManager(this));
            rvProducts.setAdapter(productAdapter);
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Error loading products: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void setupCategoryTabs() {
        if (tabCategories == null) return;

        try {
            List<String> categories = dataManager.getProductCategories();

            for (String category : categories) {
                tabCategories.addTab(tabCategories.newTab().setText(category));
            }

            tabCategories.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
                @Override
                public void onTabSelected(TabLayout.Tab tab) {
                    if (tab != null && tab.getText() != null) {
                        currentCategory = tab.getText().toString();
                        filterProducts();
                    }
                }

                @Override
                public void onTabUnselected(TabLayout.Tab tab) {
                }

                @Override
                public void onTabReselected(TabLayout.Tab tab) {
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Error setting up categories: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void setupSearchFunctionality() {
        if (etSearch != null) {
            etSearch.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    filterProducts();
                }

                @Override
                public void afterTextChanged(Editable s) {
                }
            });
        }
    }

    private void filterProducts() {
        if (etSearch == null) return;

        String searchQuery = etSearch.getText().toString().toLowerCase().trim();
        List<Product> categoryFilteredProducts;

        // First filter by category
        if ("All".equals(currentCategory)) {
            categoryFilteredProducts = new ArrayList<>(allProducts);
        } else {
            categoryFilteredProducts = dataManager.getProductsByCategory(currentCategory);
        }

        // Then filter by search query
        if (searchQuery.isEmpty()) {
            filteredProducts = categoryFilteredProducts;
        } else {
            filteredProducts = new ArrayList<>();
            for (Product product : categoryFilteredProducts) {
                if (product.getName().toLowerCase().contains(searchQuery) ||
                        product.getDescription().toLowerCase().contains(searchQuery) ||
                        product.getCategory().toLowerCase().contains(searchQuery)) {
                    filteredProducts.add(product);
                }
            }
        }

        if (productAdapter != null) {
            productAdapter.updateProducts(filteredProducts);
        }
        updateProductsCount();
    }

    private void updateProductsCount() {
        if (tvProductsCount == null || filteredProducts == null) return;

        int availableCount = 0;
        for (Product product : filteredProducts) {
            if (product.isAvailable() && product.getQuantityAvailable() > 0) {
                availableCount++;
            }
        }
        tvProductsCount.setText(String.format("%d available products", availableCount));
    }

    private void showSortOptions() {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle("Sort Products");

        String[] sortOptions = {"Name (A-Z)", "Name (Z-A)", "Price (Low to High)", "Price (High to Low)", "Category"};

        builder.setItems(sortOptions, (dialog, which) -> {
            sortProducts(which);
        });

        builder.show();
    }

    private void sortProducts(int sortType) {
        if (filteredProducts == null) return;

        switch (sortType) {
            case 0: // Name A-Z
                filteredProducts.sort((p1, p2) -> p1.getName().compareToIgnoreCase(p2.getName()));
                break;
            case 1: // Name Z-A
                filteredProducts.sort((p1, p2) -> p2.getName().compareToIgnoreCase(p1.getName()));
                break;
            case 2: // Price Low to High
                filteredProducts.sort((p1, p2) -> p1.getPrice().compareTo(p2.getPrice()));
                break;
            case 3: // Price High to Low
                filteredProducts.sort((p1, p2) -> p2.getPrice().compareTo(p1.getPrice()));
                break;
            case 4: // Category
                filteredProducts.sort((p1, p2) -> p1.getCategory().compareToIgnoreCase(p2.getCategory()));
                break;
        }

        if (productAdapter != null) {
            productAdapter.updateProducts(filteredProducts);
        }
    }

    private void filterProductsByCategory(String category) {
        currentCategory = category;
        filterProducts();
    }

    private void setupBottomNavigation() {
        if (bottomNavigation == null) {
            Toast.makeText(this, "Bottom navigation not found", Toast.LENGTH_SHORT).show();
            return;
        }

        bottomNavigation.setSelectedItemId(R.id.nav_home);
        bottomNavigation.setOnItemSelectedListener(new NavigationBarView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_home) {
                    // Already on home
                    return true;
                } else if (itemId == R.id.nav_menu) {
                    // Stay on current screen (this is the menu)
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    // Navigate to Order History
                    Intent intent = new Intent(CustomerDashboardActivity.this, OrderHistoryActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    return true;
                } else if (itemId == R.id.nav_favorites) {
                    // Show a simple message since favorites isn't implemented
                    Toast.makeText(CustomerDashboardActivity.this, "Favorites - Coming Soon!", Toast.LENGTH_SHORT).show();
                    return true;
                } else if (itemId == R.id.nav_profile) {
                    // Navigate to Profile
                    Intent intent = new Intent(CustomerDashboardActivity.this, ProfileActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    return true;
                }
                return false;
            }
        });
    }

    @Override
    public void onAddToCartClick(Product product) {
        boolean success = dataManager.addToCart(currentUserId, product.getItemId(), 1);
        if (success) {
            Toast.makeText(this, product.getName() + " added to cart!", Toast.LENGTH_SHORT).show();
            updateCartBadge();
        } else {
            Toast.makeText(this, "Unable to add item to cart. Please check stock availability.", Toast.LENGTH_SHORT).show();
        }
    }

    private void updateCartBadge() {
        if (tvCartBadge == null) return;

        int cartItemCount = dataManager.getCartItemCount(currentUserId);
        if (cartItemCount > 0) {
            tvCartBadge.setText(String.valueOf(cartItemCount));
            tvCartBadge.setVisibility(View.VISIBLE);

            // Add bounce animation if available
            try {
                android.view.animation.Animation scaleAnimation = android.view.animation.AnimationUtils
                        .loadAnimation(this, R.anim.scale_bounce);
                tvCartBadge.startAnimation(scaleAnimation);
            } catch (Exception e) {
                // Animation not available, just show the badge
            }
        } else {
            tvCartBadge.setVisibility(View.GONE);
        }
    }

    private void openCart() {
        // Check if cart has items first
        List<CartItem> cartItems = dataManager.getCartItemsByUserId(currentUserId);
        if (cartItems.isEmpty()) {
            Toast.makeText(this, "Your cart is empty", Toast.LENGTH_SHORT).show();
            return;
        }
        
        Intent intent = new Intent(this, CheckoutActivity.class);
        intent.putExtra("userId", currentUserId);
        startActivity(intent);
    }

    private void setupSwipeRefresh() {
        if (swipeRefresh != null) {
            swipeRefresh.setColorSchemeResources(
                    R.color.primary,
                    R.color.secondary,
                    R.color.accent
            );

            swipeRefresh.setOnRefreshListener(() -> {
                // Simulate refreshing data
                new android.os.Handler().postDelayed(() -> {
                    // Refresh the product list
                    setupProductList();
                    setupCategoryTabs();
                    updateCartBadge();
                    updateCurrentOrderDisplay();

                    swipeRefresh.setRefreshing(false);
                    Toast.makeText(CustomerDashboardActivity.this, "Products refreshed!", Toast.LENGTH_SHORT).show();
                }, 1000);
            });
        }
    }
    
    private void updateCurrentOrderDisplay() {
        if (cardCurrentOrder == null) return;
        
        try {
            Order currentOrder = dataManager.getUserCurrentActiveOrder(currentUserId);
            if (currentOrder != null) {
                cardCurrentOrder.setVisibility(View.VISIBLE);
                if (tvCurrentOrderId != null) {
                    tvCurrentOrderId.setText("Order #" + currentOrder.getOrderId());
                }
                if (tvCurrentOrderStatus != null) {
                    String status = currentOrder.getStatus();
                    String displayStatus = status.substring(0, 1).toUpperCase() + status.substring(1);
                    tvCurrentOrderStatus.setText(displayStatus + "...");

                    // Update track order button text and visibility
                    if (btnTrackOrder != null) {
                        btnTrackOrder.setVisibility(View.VISIBLE);
                        btnTrackOrder.setText("Track Order");
                    }
                }
            } else {
                cardCurrentOrder.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            e.printStackTrace();
            cardCurrentOrder.setVisibility(View.GONE);
        }
    }
    
    private void openOrderTracking() {
        try {
            Order currentOrder = dataManager.getUserCurrentActiveOrder(currentUserId);
            if (currentOrder != null) {
                Intent intent = new Intent(this, OrderTrackingActivity.class);
                intent.putExtra("orderId", currentOrder.getOrderId());
                intent.putExtra("userId", currentUserId);
                startActivity(intent);
            } else {
                Toast.makeText(this, "No active order found", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Error opening order tracking", Toast.LENGTH_SHORT).show();
        }
    }
}
