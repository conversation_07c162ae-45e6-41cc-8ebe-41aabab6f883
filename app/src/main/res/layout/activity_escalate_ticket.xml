<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Escalation Reason (required)"/>
        <EditText
            android:id="@+id/editEscalationReason"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:gravity="top"
            android:inputType="textMultiLine"
            android:maxLength="500"
            android:hint="Provide a detailed explanation for why the ticket is being escalated or a refund/credit is being issued."/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Recommended Action (optional)"/>
        <EditText
            android:id="@+id/editRecommendedAction"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:gravity="top"
            android:inputType="textMultiLine"
            android:maxLength="300"
            android:hint="Suggest how the admin should respond or resolve the issue (optional)."/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Current Assignee"/>
        <TextView
            android:id="@+id/textCurrentAssignee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="(Assignee Name)"
            android:textStyle="bold"
            android:paddingBottom="8dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Attachments (optional)"/>
        <Button
            android:id="@+id/btnAddAttachment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Add Attachment"/>
        <TextView
            android:id="@+id/textAttachmentInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No file selected"
            android:textSize="12sp"
            android:paddingBottom="8dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">
            <Button
                android:id="@+id/btnCancelEscalation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cancel"/>
            <Button
                android:id="@+id/btnConfirmEscalation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Confirm Escalation"
                android:layout_marginStart="16dp"/>
        </LinearLayout>
    </LinearLayout>
</ScrollView>

