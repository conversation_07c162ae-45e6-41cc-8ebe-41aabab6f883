package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.RecipeStepAdapter;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.Recipe;
import com.example.coffeeshop.models.RecipeStep;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;

import java.util.List;

public class RecipeDetailActivity extends AppCompatActivity {

    private TextView tvRecipeName, tvRecipeDescription, tvPreparationTime, tvDifficulty;
    private TextView tvIngredients, tvTimerDisplay;
    private RecyclerView rvSteps;
    private MaterialCardView cardIngredients, cardTimer;
    private MaterialButton btnStartTimer, btnStopTimer, btnBackToGuide;

    private MockDataManager dataManager;
    private RecipeStepAdapter stepAdapter;
    private Recipe recipe;
    private CountDownTimer countDownTimer;
    private boolean timerRunning = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_recipe_detail);

        initializeViews();
        setupData();
        setupRecyclerView();
        setupButtons();
        loadRecipeDetails();
    }

    private void initializeViews() {
        tvRecipeName = findViewById(R.id.tv_recipe_name);
        tvRecipeDescription = findViewById(R.id.tv_recipe_description);
        tvPreparationTime = findViewById(R.id.tv_preparation_time);
        tvDifficulty = findViewById(R.id.tv_difficulty);
        tvIngredients = findViewById(R.id.tv_ingredients);
        tvTimerDisplay = findViewById(R.id.tv_timer_display);
        rvSteps = findViewById(R.id.rv_steps);
        cardIngredients = findViewById(R.id.card_ingredients);
        cardTimer = findViewById(R.id.card_timer);
        btnStartTimer = findViewById(R.id.btn_start_timer);
        btnStopTimer = findViewById(R.id.btn_stop_timer);
        btnBackToGuide = findViewById(R.id.btn_back_to_guide);
    }

    private void setupData() {
        dataManager = MockDataManager.getInstance();
        int recipeId = getIntent().getIntExtra("recipe_id", -1);
        recipe = dataManager.getRecipeById(recipeId);
    }

    private void setupRecyclerView() {
        stepAdapter = new RecipeStepAdapter();
        rvSteps.setLayoutManager(new LinearLayoutManager(this));
        rvSteps.setAdapter(stepAdapter);
    }

    private void setupButtons() {
        btnStartTimer.setOnClickListener(v -> startTimer());
        btnStopTimer.setOnClickListener(v -> stopTimer());
        btnBackToGuide.setOnClickListener(v -> finish());
    }

    private void loadRecipeDetails() {
        if (recipe == null) {
            Toast.makeText(this, "Recipe not found", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        tvRecipeName.setText(recipe.getName());
        tvRecipeDescription.setText(recipe.getDescription());
        tvPreparationTime.setText(recipe.getFormattedTime());
        tvDifficulty.setText(recipe.getDifficulty());

        // Load ingredients
        if (recipe.getIngredients() != null && !recipe.getIngredients().isEmpty()) {
            StringBuilder ingredients = new StringBuilder();
            for (String ingredient : recipe.getIngredients()) {
                ingredients.append("• ").append(ingredient).append("\n");
            }
            tvIngredients.setText(ingredients.toString().trim());
        } else {
            cardIngredients.setVisibility(View.GONE);
        }

        // Load steps
        if (recipe.getSteps() != null) {
            stepAdapter.updateSteps(recipe.getSteps());
        }

        // Setup timer display
        tvTimerDisplay.setText(recipe.getFormattedTime());
    }

    private void startTimer() {
        if (timerRunning) return;

        long totalMillis = recipe.getPreparationTimeMinutes() * 60 * 1000L;
        
        countDownTimer = new CountDownTimer(totalMillis, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                long minutes = millisUntilFinished / 60000;
                long seconds = (millisUntilFinished % 60000) / 1000;
                tvTimerDisplay.setText(String.format("%02d:%02d", minutes, seconds));
            }

            @Override
            public void onFinish() {
                tvTimerDisplay.setText("00:00");
                timerRunning = false;
                btnStartTimer.setVisibility(View.VISIBLE);
                btnStopTimer.setVisibility(View.GONE);
                Toast.makeText(RecipeDetailActivity.this, "Recipe time completed!", Toast.LENGTH_LONG).show();
            }
        };

        countDownTimer.start();
        timerRunning = true;
        btnStartTimer.setVisibility(View.GONE);
        btnStopTimer.setVisibility(View.VISIBLE);
    }

    private void stopTimer() {
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        timerRunning = false;
        btnStartTimer.setVisibility(View.VISIBLE);
        btnStopTimer.setVisibility(View.GONE);
        tvTimerDisplay.setText(recipe.getFormattedTime());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }
}
