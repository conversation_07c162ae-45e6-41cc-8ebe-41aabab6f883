package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Patterns;
import android.widget.ArrayAdapter;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.MaterialAutoCompleteTextView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;

import com.example.coffeeshop.R;

public class AddAccountActivity extends AppCompatActivity {

    private Toolbar toolbar;
    private TextInputLayout tilUsername, tilEmail, tilPhone, tilRole, tilPassword, tilConfirmPassword;
    private TextInputEditText etUsername, etEmail, etPhone, etPassword, etConfirmPassword;
    private MaterialAutoCompleteTextView spinnerRole;
    private RadioGroup rgStatus;
    private RadioButton rbActive, rbInactive;
    private MaterialButton btnCreateAccount, btnCancel;

    private String[] roles = {"Barista", "Shipper", "Support"};

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_account);

        initViews();
        setupToolbar();
        setupRoleSpinner();
        setupValidation();
        setupClickListeners();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        tilUsername = findViewById(R.id.til_username);
        tilEmail = findViewById(R.id.til_email);
        tilPhone = findViewById(R.id.til_phone);
        tilRole = findViewById(R.id.til_role);
        tilPassword = findViewById(R.id.til_password);
        tilConfirmPassword = findViewById(R.id.til_confirm_password);

        etUsername = findViewById(R.id.et_username);
        etEmail = findViewById(R.id.et_email);
        etPhone = findViewById(R.id.et_phone);
        etPassword = findViewById(R.id.et_password);
        etConfirmPassword = findViewById(R.id.et_confirm_password);

        spinnerRole = findViewById(R.id.spinner_role);
        rgStatus = findViewById(R.id.rg_status);
        rbActive = findViewById(R.id.rb_active);
        rbInactive = findViewById(R.id.rb_inactive);

        btnCreateAccount = findViewById(R.id.btn_create_account);
        btnCancel = findViewById(R.id.btn_cancel);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
    }

    private void setupRoleSpinner() {
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_dropdown_item_1line, roles);
        spinnerRole.setAdapter(adapter);
    }

    private void setupValidation() {
        etUsername.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                validateUsername();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        etEmail.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                validateEmail();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        etPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                validatePassword();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        etConfirmPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                validateConfirmPassword();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
    }

    private void setupClickListeners() {
        btnCreateAccount.setOnClickListener(v -> createAccount());
        btnCancel.setOnClickListener(v -> onBackPressed());
    }

    private boolean validateUsername() {
        String username = etUsername.getText().toString().trim();

        if (TextUtils.isEmpty(username)) {
            tilUsername.setError("Username is required");
            return false;
        } else if (username.length() < 3) {
            tilUsername.setError("Username must be at least 3 characters");
            return false;
        } else {
            tilUsername.setError(null);
            return true;
        }
    }

    private boolean validateEmail() {
        String email = etEmail.getText().toString().trim();

        if (TextUtils.isEmpty(email)) {
            tilEmail.setError("Email is required");
            return false;
        } else if (!Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            tilEmail.setError("Please enter a valid email address");
            return false;
        } else {
            tilEmail.setError(null);
            return true;
        }
    }

    private boolean validatePassword() {
        String password = etPassword.getText().toString();

        if (TextUtils.isEmpty(password)) {
            tilPassword.setError("Password is required");
            return false;
        } else if (password.length() < 6) {
            tilPassword.setError("Password must be at least 6 characters");
            return false;
        } else {
            tilPassword.setError(null);
            validateConfirmPassword(); // Revalidate confirm password
            return true;
        }
    }

    private boolean validateConfirmPassword() {
        String password = etPassword.getText().toString();
        String confirmPassword = etConfirmPassword.getText().toString();

        if (TextUtils.isEmpty(confirmPassword)) {
            tilConfirmPassword.setError("Please confirm your password");
            return false;
        } else if (!password.equals(confirmPassword)) {
            tilConfirmPassword.setError("Passwords do not match");
            return false;
        } else {
            tilConfirmPassword.setError(null);
            return true;
        }
    }

    private boolean validateRole() {
        String role = spinnerRole.getText().toString().trim();

        if (TextUtils.isEmpty(role)) {
            tilRole.setError("Please select a role");
            return false;
        } else {
            tilRole.setError(null);
            return true;
        }
    }

    private String getSelectedStatus() {
        if (rbActive.isChecked()) {
            return "Active";
        } else if (rbInactive.isChecked()) {
            return "Inactive";
        }
        return "Active"; // Default to Active
    }

    private void createAccount() {
        // Validate all fields
        boolean isUsernameValid = validateUsername();
        boolean isEmailValid = validateEmail();
        boolean isPasswordValid = validatePassword();
        boolean isConfirmPasswordValid = validateConfirmPassword();
        boolean isRoleValid = validateRole();

        if (isUsernameValid && isEmailValid && isPasswordValid &&
                isConfirmPasswordValid && isRoleValid) {

            // Disable button to prevent double submission
            btnCreateAccount.setEnabled(false);
            btnCreateAccount.setText("Creating...");

            // Get form data
            String username = etUsername.getText().toString().trim();
            String email = etEmail.getText().toString().trim();
            String phone = etPhone.getText().toString().trim();
            String role = spinnerRole.getText().toString().trim();
            String password = etPassword.getText().toString();
            String status = getSelectedStatus();

            // TODO: Implement account creation logic
            // This could involve:
            // - Checking if username/email already exists
            // - Creating user in database
            // - Sending welcome email
            // - Returning to previous activity with result

            // Simulate account creation
            simulateAccountCreation(username, email, phone, role, password, status);
        }
    }

    private void simulateAccountCreation(String username, String email, String phone,
                                         String role, String password, String status) {
        // Simulate network delay
        btnCreateAccount.postDelayed(() -> {
            // Reset button state
            btnCreateAccount.setEnabled(true);
            btnCreateAccount.setText("Create Account");

            // Show success message with status
            String message = String.format("Account created successfully!\nUsername: %s\nRole: %s\nStatus: %s",
                    username, role, status);
            Toast.makeText(this, message, Toast.LENGTH_LONG).show();

            // Return to previous activity
            finish();
        }, 2000);
    }
}