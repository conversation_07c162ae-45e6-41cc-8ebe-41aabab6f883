package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.OrderHistoryCardAdapter;
import com.example.coffeeshop.data.MockOrderHistoryProvider;
import java.util.List;
import com.example.coffeeshop.data.MockOrderHistoryProvider.OrderHistoryItem;

public class OrderHistoryForTicketActivity extends AppCompatActivity {
    private TextView textOrderHistory;
    private RecyclerView recyclerOrderHistory;
    private int customerId;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_order_history_for_ticket);
        try {
            customerId = getIntent().getIntExtra("customerId", 1);
            textOrderHistory = findViewById(R.id.textOrderHistory);
            recyclerOrderHistory = findViewById(R.id.recyclerOrderHistory);
            // Show order history list
            textOrderHistory.setText("Order History");
            recyclerOrderHistory.setLayoutManager(new LinearLayoutManager(this));
            List<OrderHistoryItem> orders = MockOrderHistoryProvider.getOrdersForCustomer(customerId);
            if (orders == null || orders.isEmpty()) {
                textOrderHistory.setText("No order history found for this customer.");
                recyclerOrderHistory.setAdapter(null);
                return;
            }
            OrderHistoryCardAdapter adapter = new OrderHistoryCardAdapter(
                orders,
                order -> {
                    // Handle order details click if needed
                }
            );
            recyclerOrderHistory.setAdapter(adapter);
        } catch (Exception e) {
            if (textOrderHistory != null) {
                textOrderHistory.setText("An error occurred: " + e.getMessage());
            }
            e.printStackTrace();
        }
    }
}
