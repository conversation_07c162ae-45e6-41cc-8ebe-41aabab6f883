package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.OrderAdapter;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.Order;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.navigation.NavigationBarView;
import org.json.JSONArray;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class BaristaOrderQueueActivity extends AppCompatActivity implements OrderAdapter.OnOrderActionListener {

    private TextView tvQueueCount;
    private ChipGroup chipGroupFilter;
    private RecyclerView rvOrders;
    private SwipeRefreshLayout swipeRefresh;
    private View layoutEmptyState;
    private BottomNavigationView bottomNavigation;

    private MockDataManager dataManager;
    private ApiService apiService;
    private OrderAdapter orderAdapter;
    private List<Order> allOrders;
    private List<Order> filteredOrders;
    private String currentFilter = "All";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_barista_order_queue);

        dataManager = MockDataManager.getInstance();
        apiService = new ApiService();
        allOrders = new ArrayList<>();
        filteredOrders = new ArrayList<>();
        
        initViews();
        setupRecyclerView();
        setupFilters();
        setupSwipeRefresh();
        setupBottomNavigation();
        loadOrders();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadOrders();
    }

    private void initViews() {
        tvQueueCount = findViewById(R.id.tv_queue_count);
        chipGroupFilter = findViewById(R.id.chip_group_filter);
        rvOrders = findViewById(R.id.rv_orders);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        layoutEmptyState = findViewById(R.id.layout_empty_state);
        bottomNavigation = findViewById(R.id.bottom_navigation);
    }

    private void setupRecyclerView() {
        filteredOrders = new ArrayList<>();
        orderAdapter = new OrderAdapter(this, filteredOrders);
        orderAdapter.setOnOrderActionListener(this);
        rvOrders.setLayoutManager(new LinearLayoutManager(this));
        rvOrders.setAdapter(orderAdapter);
    }

    private void setupFilters() {
        chipGroupFilter.setOnCheckedStateChangeListener((group, checkedIds) -> {
            if (checkedIds.isEmpty()) return;
            
            int checkedId = checkedIds.get(0);
            if (checkedId == R.id.chip_all) {
                currentFilter = "All";
            } else if (checkedId == R.id.chip_pending) {
                currentFilter = "Pending";
            } else if (checkedId == R.id.chip_priority) {
                currentFilter = "Priority";
            }
            filterOrders();
        });
    }

    private void setupSwipeRefresh() {
        swipeRefresh.setColorSchemeResources(R.color.primary, R.color.secondary);
        swipeRefresh.setOnRefreshListener(() -> {
            loadOrders();
            swipeRefresh.setRefreshing(false);
        });
    }

    private void loadOrders() {
        // Load orders from API first, fallback to MockDataManager
        apiService.getActiveOrders(new ApiService.OrdersCallback() {
            @Override
            public void onSuccess(JSONArray ordersArray) {
                try {
                    allOrders = parseOrders(ordersArray);
                    filterOrders();
                    updateQueueCount();
                } catch (Exception e) {
                    e.printStackTrace();
                    loadOrdersFromMock();
                }
            }

            @Override
            public void onError(String error) {
                loadOrdersFromMock();
            }
        });
    }
    
    private void loadOrdersFromMock() {
        allOrders = dataManager.getAllOrders();
        filterOrders();
        updateQueueCount();
    }
    
    private List<Order> parseOrders(JSONArray ordersArray) throws Exception {
        List<Order> orders = new ArrayList<>();
        
        for (int i = 0; i < ordersArray.length(); i++) {
            JSONObject orderJson = ordersArray.getJSONObject(i);
            
            // Parse date from API format
            Date orderDate;
            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                String dateField = orderJson.has("order_date") ? "order_date" : "created_at";
                orderDate = dateFormat.parse(orderJson.getString(dateField));
            } catch (Exception e) {
                orderDate = new Date(); // Fallback to current date
            }
            
            Order order = new Order(
                orderJson.getInt("order_id"),
                orderJson.getInt("customer_id"),
                orderJson.getString("customer_name"),
                orderJson.getString("status"),
                new BigDecimal(orderJson.has("total_amount") ? orderJson.getDouble("total_amount") : orderJson.getDouble("total_price")),
                null, // discount
                orderDate
            );
            
            // Set additional fields from API response
            if (orderJson.has("items_summary")) {
                order.setItemsSummary(orderJson.getString("items_summary"));
            }
            
            orders.add(order);
        }
        
        return orders;
    }

    private void filterOrders() {
        filteredOrders.clear();
        
        for (Order order : allOrders) {
            boolean shouldInclude = false;
            
            switch (currentFilter) {
                case "All":
                    shouldInclude = !"finished".equals(order.getStatus()) && !"cancelled".equals(order.getStatus());
                    break;
                case "Pending":
                    shouldInclude = "pending".equals(order.getStatus());
                    break;
                case "Priority":
                    // Show orders older than 15 minutes as priority
                    long orderAge = System.currentTimeMillis() - order.getCreatedAt().getTime();
                    shouldInclude = orderAge > (15 * 60 * 1000) && !"finished".equals(order.getStatus());
                    break;
            }
            
            if (shouldInclude) {
                filteredOrders.add(order);
            }
        }
        
        orderAdapter.notifyDataSetChanged();
        updateEmptyState();
    }

    private void updateQueueCount() {
        // Count pending orders from the current loaded orders
        int pendingCount = 0;
        for (Order order : allOrders) {
            if ("pending".equals(order.getStatus())) {
                pendingCount++;
            }
        }
        tvQueueCount.setText(String.valueOf(pendingCount));
    }

    private void updateEmptyState() {
        if (filteredOrders.isEmpty()) {
            layoutEmptyState.setVisibility(View.VISIBLE);
            rvOrders.setVisibility(View.GONE);
        } else {
            layoutEmptyState.setVisibility(View.GONE);
            rvOrders.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onUpdateStatus(Order order) {
        // Simple status progression: pending -> preparing -> ready -> finished
        String currentStatus = order.getStatus();
        String newStatus;
        
        switch (currentStatus.toLowerCase()) {
            case "pending":
                newStatus = "preparing";
                break;
            case "preparing":
                newStatus = "ready";
                break;
            case "ready":
                newStatus = "finished";
                break;
            default:
                Toast.makeText(this, "Order already finished", Toast.LENGTH_SHORT).show();
                return;
        }
        
        // Update order status via API first, fallback to MockDataManager
        apiService.updateOrderStatus(order.getOrderId(), newStatus, new ApiService.OrderActionCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                Toast.makeText(BaristaOrderQueueActivity.this, "Order #" + order.getOrderId() + " updated to " + newStatus, Toast.LENGTH_SHORT).show();
                loadOrders(); // Refresh the list
            }

            @Override
            public void onError(String error) {
                // Fallback to MockDataManager
                boolean success = dataManager.updateOrderStatus(order.getOrderId(), newStatus);
                if (success) {
                    Toast.makeText(BaristaOrderQueueActivity.this, "Order #" + order.getOrderId() + " updated to " + newStatus + " (offline)", Toast.LENGTH_SHORT).show();
                    loadOrders(); // Refresh the list
                } else {
                    Toast.makeText(BaristaOrderQueueActivity.this, "Failed to update status", Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    @Override
    public void onViewDetails(Order order) {
        // Details view removed - simplified workflow uses only status updates
        Toast.makeText(this, "Use the status button to manage this order", Toast.LENGTH_SHORT).show();
    }

    private void setupBottomNavigation() {
        if (bottomNavigation == null) return;

        bottomNavigation.setSelectedItemId(R.id.nav_orders);
        bottomNavigation.setOnItemSelectedListener(new NavigationBarView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_dashboard) {
                    startActivity(new Intent(BaristaOrderQueueActivity.this, BaristaDashboardActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    return true;
                } else if (itemId == R.id.nav_recipes) {
                    startActivity(new Intent(BaristaOrderQueueActivity.this, BaristaRecipeGuideActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_inventory) {
                    startActivity(new Intent(BaristaOrderQueueActivity.this, BaristaInventoryActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_ready) {
                    startActivity(new Intent(BaristaOrderQueueActivity.this, BaristaReadyOrdersActivity.class));
                    finish();
                    return true;
                }
                return false;
            }
        });
    }
}
