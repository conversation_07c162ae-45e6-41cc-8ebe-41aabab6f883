package com.example.coffeeshop.data;

import com.example.coffeeshop.models.CartItem;
import com.example.coffeeshop.models.DailyTask;
import com.example.coffeeshop.models.InventoryItem;
import com.example.coffeeshop.models.Order;
import com.example.coffeeshop.models.OrderItem;
import com.example.coffeeshop.models.Product;
import com.example.coffeeshop.models.Recipe;
import com.example.coffeeshop.models.RecipeStep;
import com.example.coffeeshop.models.Review;
import com.example.coffeeshop.models.Role;
import com.example.coffeeshop.models.User;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class MockDataManager {
    private static MockDataManager instance;
    private List<User> users;
    private List<Product> products;
    private List<CartItem> cartItems;
    private List<Order> orders;
    private List<Order> orderHistory; // For finished orders
    private List<OrderItem> orderItems;
    private List<InventoryItem> inventoryItems;
    private List<DailyTask> dailyTasks;
    private List<Recipe> recipes;
    private List<Review> reviews;
    private int nextUserId = 6; // Start from 6 since we have 5 predefined users
    private int nextProductId = 31; // Start from 31 since we have 30 predefined products
    private int nextCartItemId = 1;
    private int nextOrderId = 1;
    private int nextOrderItemId = 1;
    private int nextReviewId = 1;
    private int nextTaskId = 1; // For daily tasks

    private static final String DEFAULT_IMAGE_URL = "https://media.istockphoto.com/id/1467199060/photo/cup-of-coffee-with-smoke-and-coffee-beans-on-old-wooden-background.jpg?s=612x612&w=0&k=20&c=OnS8_6FM5ussfSGmjpDD-GofERg2UbItdxShIAA90sQ=";

    private MockDataManager() {
        initializeMockData();
    }

    public static synchronized MockDataManager getInstance() {
        if (instance == null) {
            instance = new MockDataManager();
        }
        return instance;
    }

    private void initializeMockData() {
        users = new ArrayList<>();
        products = new ArrayList<>();
        cartItems = new ArrayList<>();
        orders = new ArrayList<>();
        orderHistory = new ArrayList<>(); // Initialize order history
        orderItems = new ArrayList<>();
        inventoryItems = new ArrayList<>();
        dailyTasks = new ArrayList<>();
        reviews = new ArrayList<>();
        recipes = new ArrayList<>();
        Date now = new Date();

        // Predefined users for each role
        users.add(new User(1, "John Customer", "<EMAIL>", "123-456-7890", "password123", Role.CUSTOMER, now));
        users.add(new User(2, "Sarah Barista", "<EMAIL>", "123-456-7891", "password123", Role.BARISTA, now));
        users.add(new User(3, "Mike Shipper", "<EMAIL>", "123-456-7892", "password123", Role.SHIPPER, now));
        users.add(new User(4, "Emily Manager", "<EMAIL>", "123-456-7893", "password123", Role.MANAGER, now));
        users.add(new User(5, "David Support", "<EMAIL>", "123-456-7894", "password123", Role.CUSTOMER_SUPPORT, now));

        // Predefined products
        initializeProducts(now);

        // Initialize recipes
        initializeSampleRecipes();

        // Initialize sample reviews for products
        initializeSampleReviews();

        // Initialize sample orders for barista testing
        initializeSampleOrders();
        
        // Initialize inventory items
        initializeInventoryItems();
        
        // Initialize daily tasks
        initializeDailyTasks();
        
        // Initialize sample recipes
        initializeSampleRecipes();
    }

    private void initializeProducts(Date now) {
        // Espresso Drinks
        products.add(new Product(1, "Espresso", "Rich and bold single shot espresso", new BigDecimal("2.50"), "Espresso", true, 50, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(2, "Double Espresso", "Double shot of rich espresso", new BigDecimal("3.50"), "Espresso", true, 50, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(3, "Americano", "Espresso with hot water", new BigDecimal("3.00"), "Espresso", true, 45, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(4, "Long Black", "Double shot espresso with hot water", new BigDecimal("3.25"), "Espresso", true, 40, now, 4, DEFAULT_IMAGE_URL));

        // Milk-Based Coffee
        products.add(new Product(5, "Cappuccino", "Espresso with steamed milk and thick foam", new BigDecimal("4.00"), "Milk Coffee", true, 40, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(6, "Latte", "Espresso with steamed milk and light foam", new BigDecimal("4.50"), "Milk Coffee", true, 35, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(7, "Flat White", "Double shot espresso with steamed milk", new BigDecimal("4.25"), "Milk Coffee", true, 30, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(8, "Macchiato", "Espresso 'marked' with steamed milk", new BigDecimal("3.75"), "Milk Coffee", true, 25, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(9, "Mocha", "Espresso with chocolate and steamed milk", new BigDecimal("5.00"), "Milk Coffee", true, 20, now, 4, DEFAULT_IMAGE_URL));

        // Cold Beverages
        products.add(new Product(10, "Iced Coffee", "Cold brew coffee over ice", new BigDecimal("3.50"), "Cold Drinks", true, 30, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(11, "Iced Latte", "Chilled espresso with cold milk", new BigDecimal("4.75"), "Cold Drinks", true, 25, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(12, "Frappuccino", "Blended coffee with ice and whipped cream", new BigDecimal("5.50"), "Cold Drinks", true, 25, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(13, "Cold Brew", "Slow-steeped cold coffee", new BigDecimal("4.00"), "Cold Drinks", true, 20, now, 4, DEFAULT_IMAGE_URL));

        // Specialty Drinks
        products.add(new Product(14, "Caramel Macchiato", "Vanilla latte with caramel drizzle", new BigDecimal("5.25"), "Specialty", true, 15, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(15, "Vanilla Latte", "Latte with vanilla syrup", new BigDecimal("4.75"), "Specialty", true, 20, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(16, "Chai Latte", "Spiced tea with steamed milk", new BigDecimal("4.50"), "Specialty", true, 18, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(17, "Hot Chocolate", "Rich chocolate with steamed milk", new BigDecimal("3.75"), "Specialty", true, 22, now, 4, DEFAULT_IMAGE_URL));

        // Pastries & Baked Goods
        products.add(new Product(18, "Croissant", "Buttery, flaky French pastry", new BigDecimal("3.25"), "Pastries", true, 20, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(19, "Pain au Chocolat", "Chocolate-filled croissant", new BigDecimal("3.75"), "Pastries", true, 15, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(20, "Blueberry Muffin", "Fresh blueberry muffin", new BigDecimal("2.75"), "Pastries", true, 15, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(21, "Banana Bread", "Moist banana bread slice", new BigDecimal("3.00"), "Pastries", true, 12, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(22, "Danish Pastry", "Sweet Danish with fruit filling", new BigDecimal("3.50"), "Pastries", true, 10, now, 4, DEFAULT_IMAGE_URL));

        // Sandwiches & Light Meals
        products.add(new Product(23, "Club Sandwich", "Triple-layer sandwich with turkey and bacon", new BigDecimal("8.50"), "Food", true, 10, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(24, "Grilled Panini", "Pressed sandwich with cheese and ham", new BigDecimal("7.25"), "Food", true, 12, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(25, "Bagel with Cream Cheese", "Toasted everything bagel", new BigDecimal("3.75"), "Food", true, 18, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(26, "Avocado Toast", "Smashed avocado on sourdough", new BigDecimal("6.50"), "Food", true, 8, now, 4, DEFAULT_IMAGE_URL));

        // Snacks & Treats
        products.add(new Product(27, "Chocolate Chip Cookie", "Homemade chocolate chip cookie", new BigDecimal("2.00"), "Snacks", true, 25, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(28, "Brownies", "Fudgy chocolate brownies", new BigDecimal("2.50"), "Snacks", true, 20, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(29, "Granola Bar", "Healthy oats and nuts bar", new BigDecimal("2.75"), "Snacks", true, 15, now, 4, DEFAULT_IMAGE_URL));
        products.add(new Product(30, "Fruit Cup", "Fresh seasonal fruit mix", new BigDecimal("4.25"), "Snacks", true, 12, now, 4, DEFAULT_IMAGE_URL));
    }

    public List<User> getAllUsers() {
        return new ArrayList<>(users);
    }

    public User findUserByEmail(String email) {
        for (User user : users) {
            if (user.getEmail().equalsIgnoreCase(email)) {
                return user;
            }
        }
        return null;
    }

    public boolean authenticateUser(String email, String password) {
        User user = findUserByEmail(email);
        return user != null && user.getPasswordHash().equals(password);
    }

    public User loginUser(String email, String password) {
        if (authenticateUser(email, password)) {
            return findUserByEmail(email);
        }
        return null;
    }

    public boolean isEmailExists(String email) {
        return findUserByEmail(email) != null;
    }

    public User registerUser(String fullName, String email, String phone, String password) {
        if (isEmailExists(email)) {
            return null; // Email already exists
        }

        User newUser = new User(nextUserId++, fullName, email, phone, password, Role.CUSTOMER, new Date());
        users.add(newUser);
        return newUser;
    }

    public void resetData() {
        users.clear();
        products.clear();
        cartItems.clear();
        orders.clear();
        orderItems.clear();
        nextUserId = 6;
        nextProductId = 11;
        nextCartItemId = 1;
        nextOrderId = 1;
        nextOrderItemId = 1;
        initializeMockData();
    }

    // Product management methods
    public List<Product> getAllProducts() {
        return new ArrayList<>(products);
    }

    public List<Product> getProductsByCategory(String category) {
        List<Product> filteredProducts = new ArrayList<>();
        for (Product product : products) {
            if (product.getCategory().equalsIgnoreCase(category)) {
                filteredProducts.add(product);
            }
        }
        return filteredProducts;
    }

    public Product findProductById(int productId) {
        for (Product product : products) {
            if (product.getItemId() == productId) {
                return product;
            }
        }
        return null;
    }

    public List<String> getProductCategories() {
        List<String> categories = new ArrayList<>();
        categories.add("All");
        for (Product product : products) {
            if (!categories.contains(product.getCategory())) {
                categories.add(product.getCategory());
            }
        }
        return categories;
    }

    // Cart management methods
    public List<CartItem> getCartItems(int userId) {
        List<CartItem> userCartItems = new ArrayList<>();
        for (CartItem item : cartItems) {
            if (item.getUserId() == userId) {
                userCartItems.add(item);
            }
        }
        return userCartItems;
    }

    public boolean addToCart(int userId, int productId, int quantity) {
        Product product = findProductById(productId);
        if (product == null || !product.isAvailable() || product.getQuantityAvailable() < quantity) {
            return false;
        }

        // Check if item already exists in cart
        CartItem existingItem = findCartItem(userId, productId);
        if (existingItem != null) {
            // Update quantity
            int newQuantity = existingItem.getQuantity() + quantity;
            if (newQuantity <= product.getQuantityAvailable()) {
                existingItem.setQuantity(newQuantity);
                return true;
            }
            return false;
        }

        // Add new cart item
        CartItem newItem = new CartItem(nextCartItemId++, userId, productId, product, quantity, product.getPrice(), new Date());
        cartItems.add(newItem);
        return true;
    }

    public boolean updateCartItemQuantity(int cartItemId, int newQuantity) {
        CartItem cartItem = findCartItemById(cartItemId);
        if (cartItem == null) return false;

        if (newQuantity <= 0) {
            cartItems.remove(cartItem);
            return true;
        }

        Product product = cartItem.getProduct();
        if (newQuantity <= product.getQuantityAvailable()) {
            cartItem.setQuantity(newQuantity);
            return true;
        }
        return false;
    }

    public boolean removeFromCart(int cartItemId) {
        CartItem cartItem = findCartItemById(cartItemId);
        if (cartItem != null) {
            cartItems.remove(cartItem);
            return true;
        }
        return false;
    }

    public void clearCart(int userId) {
        cartItems.removeIf(item -> item.getUserId() == userId);
    }

    // Additional missing methods
    public List<CartItem> getCartItemsByUserId(int userId) {
        List<CartItem> userCartItems = new ArrayList<>();
        for (CartItem item : cartItems) {
            if (item.getUserId() == userId) {
                userCartItems.add(item);
            }
        }
        return userCartItems;
    }

    public int getCartItemCount(int userId) {
        int count = 0;
        for (CartItem item : cartItems) {
            if (item.getUserId() == userId) {
                count += item.getQuantity();
            }
        }
        return count;
    }

    public BigDecimal getCartTotal(int userId) {
        BigDecimal total = BigDecimal.ZERO;
        for (CartItem item : cartItems) {
            if (item.getUserId() == userId) {
                total = total.add(item.getTotalPrice());
            }
        }
        return total;
    }

    public Product getProductById(int productId) {
        return findProductById(productId);
    }

    public int getNextCartItemId() {
        return nextCartItemId++;
    }

    public List<Review> getProductReviews(int productId) {
        List<Review> productReviews = new ArrayList<>();
        for (Review review : reviews) {
            if (review.getProductId() == productId) {
                productReviews.add(review);
            }
        }
        return productReviews;
    }

    public void addOrder(Order order) {
        order.setOrderId(nextOrderId++);

        // Set order item IDs and link them to the order
        for (OrderItem item : order.getOrderItems()) {
            item.setOrderItemId(nextOrderItemId++);
            item.setOrderId(order.getOrderId());
            orderItems.add(item);
        }

        orders.add(order);
    }

    private CartItem findCartItem(int userId, int productId) {
        for (CartItem item : cartItems) {
            if (item.getUserId() == userId && item.getProductId() == productId) {
                return item;
            }
        }
        return null;
    }

    private CartItem findCartItemById(int cartItemId) {
        for (CartItem item : cartItems) {
            if (item.getCartItemId() == cartItemId) {
                return item;
            }
        }
        return null;
    }

    // Review Management Methods
    private void initializeSampleReviews() {
        Date now = new Date();

        // Sample reviews for various products
        reviews.add(new Review(nextReviewId++, 1, "Sarah Johnson", 5, "Amazing espresso! Perfect strength and rich flavor.", new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000L)));
        reviews.add(new Review(nextReviewId++, 1, "Mike Chen", 4, "Great coffee, though a bit strong for my taste.", new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000L)));
        reviews.add(new Review(nextReviewId++, 1, "Emma Davis", 5, "Best espresso in town! Will definitely order again.", new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000L)));

        reviews.add(new Review(nextReviewId++, 2, "Alex Smith", 5, "Creamy and delicious latte. Perfect temperature!", new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000L)));
        reviews.add(new Review(nextReviewId++, 2, "Lisa Wong", 4, "Good latte but could use a bit more foam.", new Date(now.getTime() - 4 * 24 * 60 * 60 * 1000L)));

        reviews.add(new Review(nextReviewId++, 3, "David Miller", 4, "Nice americano, good value for money.", new Date(now.getTime() - 8 * 24 * 60 * 60 * 1000L)));
        reviews.add(new Review(nextReviewId++, 3, "Julia Brown", 5, "Perfect americano! Just the right balance.", new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000L)));

        reviews.add(new Review(nextReviewId++, 5, "Tom Wilson", 5, "Excellent cappuccino with beautiful latte art!", new Date(now.getTime() - 9 * 24 * 60 * 60 * 1000L)));
        reviews.add(new Review(nextReviewId++, 5, "Amy Taylor", 4, "Good cappuccino, loved the presentation.", new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000L)));

        reviews.add(new Review(nextReviewId++, 15, "Chris Lee", 5, "Delicious croissant! Fresh and buttery.", new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000L)));
        reviews.add(new Review(nextReviewId++, 15, "Maria Garcia", 4, "Good croissant, pairs well with coffee.", new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000L)));
    }

    // Order Management Methods
    private void initializeSampleOrders() {
        Date now = new Date();

        // Sample Order 1 - Pending
        Order order1 = new Order(nextOrderId++, 1, "Alice Johnson", "pending",
                new BigDecimal("15.50"), null, new Date(now.getTime() - 10 * 60 * 1000));
        orders.add(order1);

        List<OrderItem> order1Items = new ArrayList<>();
        order1Items.add(new OrderItem(nextOrderItemId++, order1.getOrderId(), 1, "Espresso", 2, new BigDecimal("3.50")));
        order1Items.add(new OrderItem(nextOrderItemId++, order1.getOrderId(), 5, "Cappuccino", 1, new BigDecimal("4.50")));
        order1Items.add(new OrderItem(nextOrderItemId++, order1.getOrderId(), 18, "Croissant", 1, new BigDecimal("3.25")));
        order1.setOrderItems(order1Items);
        orderItems.addAll(order1Items);

        // Sample Order 2 - Preparing
        Order order2 = new Order(nextOrderId++, 1, "Bob Smith", "preparing",
                new BigDecimal("12.00"), null, new Date(now.getTime() - 5 * 60 * 1000));
        orders.add(order2);

        List<OrderItem> order2Items = new ArrayList<>();
        order2Items.add(new OrderItem(nextOrderItemId++, order2.getOrderId(), 3, "Americano", 1, new BigDecimal("3.00")));
        order2Items.add(new OrderItem(nextOrderItemId++, order2.getOrderId(), 14, "Caramel Macchiato", 1, new BigDecimal("5.25")));
        order2Items.add(new OrderItem(nextOrderItemId++, order2.getOrderId(), 20, "Blueberry Muffin", 1, new BigDecimal("2.75")));
        order2.setOrderItems(order2Items);
        orderItems.addAll(order2Items);

        // Sample Order 3 - Pending
        Order order3 = new Order(nextOrderId++, 1, "Carol Davis", "pending",
                new BigDecimal("8.50"), null, new Date(now.getTime() - 2 * 60 * 1000));
        orders.add(order3);

        List<OrderItem> order3Items = new ArrayList<>();
        order3Items.add(new OrderItem(nextOrderItemId++, order3.getOrderId(), 6, "Latte", 1, new BigDecimal("4.50")));
        order3Items.add(new OrderItem(nextOrderItemId++, order3.getOrderId(), 21, "Banana Bread", 1, new BigDecimal("3.00")));
        order3.setOrderItems(order3Items);
        orderItems.addAll(order3Items);

        // Sample Order 4 - Preparing
        Order order4 = new Order(nextOrderId++, 1, "David Wilson", "preparing",
                new BigDecimal("7.00"), null, new Date(now.getTime() - 1 * 60 * 1000));
        orders.add(order4);

        List<OrderItem> order4Items = new ArrayList<>();
        order4Items.add(new OrderItem(nextOrderItemId++, order4.getOrderId(), 9, "Mocha", 1, new BigDecimal("5.00")));
        order4Items.add(new OrderItem(nextOrderItemId++, order4.getOrderId(), 27, "Chocolate Chip Cookie", 1, new BigDecimal("2.00")));
        order4.setOrderItems(order4Items);
        orderItems.addAll(order4Items);



        // Sample finished orders (moved to history)
        Order finishedOrder1 = new Order(nextOrderId++, 1, "Eve Brown", "finished",
                new BigDecimal("9.50"), null, new Date(now.getTime() - 30 * 60 * 1000));
        List<OrderItem> finished1Items = new ArrayList<>();
        finished1Items.add(new OrderItem(nextOrderItemId++, finishedOrder1.getOrderId(), 6, "Latte", 2, new BigDecimal("4.50")));
        finished1Items.add(new OrderItem(nextOrderItemId++, finishedOrder1.getOrderId(), 27, "Chocolate Chip Cookie", 1, new BigDecimal("2.00")));
        finishedOrder1.setOrderItems(finished1Items);
        orderHistory.add(finishedOrder1);
        orderItems.addAll(finished1Items);

        Order finishedOrder2 = new Order(nextOrderId++, 1, "Frank Miller", "finished",
                new BigDecimal("11.00"), null, new Date(now.getTime() - 45 * 60 * 1000));
        List<OrderItem> finished2Items = new ArrayList<>();
        finished2Items.add(new OrderItem(nextOrderItemId++, finishedOrder2.getOrderId(), 1, "Espresso", 1, new BigDecimal("2.50")));
        finished2Items.add(new OrderItem(nextOrderItemId++, finishedOrder2.getOrderId(), 9, "Mocha", 1, new BigDecimal("5.00")));
        finished2Items.add(new OrderItem(nextOrderItemId++, finishedOrder2.getOrderId(), 20, "Blueberry Muffin", 1, new BigDecimal("2.75")));
        finishedOrder2.setOrderItems(finished2Items);
        orderHistory.add(finishedOrder2);
        orderItems.addAll(finished2Items);

        Order finishedOrder3 = new Order(nextOrderId++, 1, "Anna Davis", "finished",
                new BigDecimal("15.25"), null, new Date(now.getTime() - 60 * 60 * 1000));
        List<OrderItem> finished3Items = new ArrayList<>();
        finished3Items.add(new OrderItem(nextOrderItemId++, finishedOrder3.getOrderId(), 14, "Caramel Macchiato", 2, new BigDecimal("5.25")));
        finished3Items.add(new OrderItem(nextOrderItemId++, finishedOrder3.getOrderId(), 23, "Club Sandwich", 1, new BigDecimal("8.50")));
        finished3Items.add(new OrderItem(nextOrderItemId++, finishedOrder3.getOrderId(), 28, "Brownies", 1, new BigDecimal("2.50")));
        finishedOrder3.setOrderItems(finished3Items);
        orderHistory.add(finishedOrder3);
        orderItems.addAll(finished3Items);

        Order finishedOrder4 = new Order(nextOrderId++, 1, "Mark Johnson", "finished",
                new BigDecimal("12.75"), null, new Date(now.getTime() - 75 * 60 * 1000));
        List<OrderItem> finished4Items = new ArrayList<>();
        finished4Items.add(new OrderItem(nextOrderItemId++, finishedOrder4.getOrderId(), 11, "Iced Latte", 1, new BigDecimal("4.75")));
        finished4Items.add(new OrderItem(nextOrderItemId++, finishedOrder4.getOrderId(), 24, "Grilled Panini", 1, new BigDecimal("7.25")));
        finished4Items.add(new OrderItem(nextOrderItemId++, finishedOrder4.getOrderId(), 27, "Chocolate Chip Cookie", 1, new BigDecimal("2.00")));
        finishedOrder4.setOrderItems(finished4Items);
        orderHistory.add(finishedOrder4);
        orderItems.addAll(finished4Items);
    }

    public List<Order> getAllOrders() {
        return new ArrayList<>(orders);
    }

    public List<Order> getOrdersByStatus(String status) {
        List<Order> filteredOrders = new ArrayList<>();
        for (Order order : orders) {
            if (order.getStatus().equals(status)) {
                filteredOrders.add(order);
            }
        }
        return filteredOrders;
    }

    public List<Order> getPendingOrders() {
        List<Order> pendingOrders = new ArrayList<>();
        for (Order order : orders) {
            if (order.canBeUpdated()) {
                pendingOrders.add(order);
            }
        }
        return pendingOrders;
    }

    public Order getOrderById(int orderId) {
        for (Order order : orders) {
            if (order.getOrderId() == orderId) {
                return order;
            }
        }
        return null;
    }

    public boolean updateOrderStatus(int orderId, String newStatus) {
        Order order = getOrderById(orderId);
        if (order != null && order.canBeUpdated()) {
            order.setStatus(newStatus);

            // If order is finished, move it to history
            if ("finished".equals(newStatus)) {
                orders.remove(order);
                orderHistory.add(order);
            }

            return true;
        }
        return false;
    }

    public List<Order> getOrderHistory() {
        return new ArrayList<>(orderHistory);
    }

    public List<Order> getActiveOrders() {
        // Returns only pending and preparing orders (excludes finished)
        List<Order> activeOrders = new ArrayList<>();
        for (Order order : orders) {
            if (!"finished".equals(order.getStatus())) {
                activeOrders.add(order);
            }
        }
        return activeOrders;
    }

    public List<OrderItem> getOrderItems(int orderId) {
        List<OrderItem> items = new ArrayList<>();
        for (OrderItem item : orderItems) {
            if (item.getOrderId() == orderId) {
                items.add(item);
            }
        }
        return items;
    }

    // Additional methods for customer screens

    public Order getUserCurrentActiveOrder(int userId) {
        // Find the most recent active order for this user (not finished/delivered)
        Order currentOrder = null;
        Date latestDate = null;

        for (Order order : orders) {
            if (order.getCustomerId() == userId &&
                    !"finished".equals(order.getStatus()) &&
                    !"delivered".equals(order.getStatus()) &&
                    !"cancelled".equals(order.getStatus())) {

                if (latestDate == null || order.getCreatedAt().after(latestDate)) {
                    currentOrder = order;
                    latestDate = order.getCreatedAt();
                }
            }
        }
        return currentOrder;
    }

    public List<Order> getOrderHistoryByUserIdSince(int userId, Date since) {
        List<Order> userOrderHistory = new ArrayList<>();

        for (Order order : orderHistory) {
            if (order.getCustomerId() == userId && order.getCreatedAt().after(since)) {
                userOrderHistory.add(order);
            }
        }
        // Also check orders list for finished orders
        for (Order order : orders) {
            if (order.getCustomerId() == userId && order.getCreatedAt().after(since) &&
                    ("finished".equals(order.getStatus()) || "delivered".equals(order.getStatus()))) {
                userOrderHistory.add(order);
            }
        }
        return userOrderHistory;
    }

    public Order findOrderById(int orderId) {
        // Check active orders first
        for (Order order : orders) {
            if (order.getOrderId() == orderId) {
                return order;
            }
        }
        // Check order history
        for (Order order : orderHistory) {
            if (order.getOrderId() == orderId) {
                return order;
            }
        }
        return null;
    }

    public User findUserById(int userId) {
        for (User user : users) {
            if (user.getUserId() == userId) {
                return user;
            }
        }
        return null;
    }

    public boolean updateUser(User user) {
        for (int i = 0; i < users.size(); i++) {
            if (users.get(i).getUserId() == user.getUserId()) {
                users.set(i, user);
                return true;
            }
        }
        return false;
    }

    public boolean reorderItems(int orderId, int userId) {
        Order order = findOrderById(orderId);
        if (order == null || order.getOrderItems() == null) {
            return false;
        }

        try {
            // Add order items back to cart
            for (OrderItem orderItem : order.getOrderItems()) {
                Product product = findProductById(orderItem.getItemId());
                if (product != null) {
                    addToCart(userId, orderItem.getItemId(), orderItem.getQuantity());
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    // Barista-specific methods
    
    private void initializeInventoryItems() {
        inventoryItems.add(new InventoryItem(1, "Coffee Beans (Espresso)", 45, 10, "kg"));
        inventoryItems.add(new InventoryItem(2, "Coffee Beans (House Blend)", 38, 10, "kg"));
        inventoryItems.add(new InventoryItem(3, "Milk", 25, 5, "liters"));
        inventoryItems.add(new InventoryItem(4, "Almond Milk", 8, 3, "liters"));
        inventoryItems.add(new InventoryItem(5, "Oat Milk", 12, 3, "liters"));
        inventoryItems.add(new InventoryItem(6, "Sugar", 20, 5, "kg"));
        inventoryItems.add(new InventoryItem(7, "Vanilla Syrup", 15, 3, "bottles"));
        inventoryItems.add(new InventoryItem(8, "Caramel Syrup", 12, 3, "bottles"));
        inventoryItems.add(new InventoryItem(9, "Chocolate Syrup", 18, 3, "bottles"));
        inventoryItems.add(new InventoryItem(10, "Whipped Cream", 6, 2, "cans"));
        inventoryItems.add(new InventoryItem(11, "Pastry Flour", 25, 5, "kg"));
        inventoryItems.add(new InventoryItem(12, "Butter", 8, 2, "kg"));
        inventoryItems.add(new InventoryItem(13, "Eggs", 24, 12, "pieces"));
        inventoryItems.add(new InventoryItem(14, "Disposable Cups (Small)", 150, 50, "pieces"));
        inventoryItems.add(new InventoryItem(15, "Disposable Cups (Large)", 120, 50, "pieces"));
        inventoryItems.add(new InventoryItem(16, "Cup Lids", 180, 100, "pieces"));
    }
    
    public List<InventoryItem> getAllInventoryItems() {
        return new ArrayList<>(inventoryItems);
    }
    
    public List<InventoryItem> getLowStockItems() {
        List<InventoryItem> lowStockItems = new ArrayList<>();
        for (InventoryItem item : inventoryItems) {
            if (item.isLowStock()) {
                lowStockItems.add(item);
            }
        }
        return lowStockItems;
    }
    
    public List<InventoryItem> getOutOfStockItems() {
        List<InventoryItem> outOfStockItems = new ArrayList<>();
        for (InventoryItem item : inventoryItems) {
            if (item.isOutOfStock()) {
                outOfStockItems.add(item);
            }
        }
        return outOfStockItems;
    }
    
    public boolean updateInventoryStock(int itemId, int newStock) {
        for (InventoryItem item : inventoryItems) {
            if (item.getItemId() == itemId) {
                item.setCurrentStock(newStock);
                return true;
            }
        }
        return false;
    }
    
    public List<Order> getPreparingOrders() {
        return getOrdersByStatus("preparing");
    }
    
    public List<Order> getReadyOrders() {
        return getOrdersByStatus("ready");
    }
    
    public int getTodayCompletedOrderCount() {
        // For demo purposes, return a random number
        return 15 + (int)(Math.random() * 10);
    }
    
    public boolean claimOrder(int orderId, String baristaName) {
        Order order = getOrderById(orderId);
        if (order != null && "pending".equals(order.getStatus())) {
            order.setStatus("preparing");
            return true;
        }
        return false;
    }

    private void initializeDailyTasks() {
        dailyTasks.add(new DailyTask("TASK001", "Clean Coffee Machine", "Daily maintenance and cleaning of the espresso machine", "15 minutes", "high"));
        dailyTasks.add(new DailyTask("TASK002", "Restock Cups and Napkins", "Check and refill cups, napkins, and other supplies", "10 minutes", "medium"));
        dailyTasks.add(new DailyTask("TASK003", "Check Milk Inventory", "Verify milk levels and freshness dates", "5 minutes", "high"));
        dailyTasks.add(new DailyTask("TASK004", "Sanitize Work Surfaces", "Clean and sanitize all work areas and counters", "20 minutes", "high"));
        dailyTasks.add(new DailyTask("TASK005", "Update Menu Board", "Check daily specials and update menu displays", "10 minutes", "low"));
        dailyTasks.add(new DailyTask("TASK006", "Organize Storage Area", "Tidy up storage and check for expired items", "15 minutes", "medium"));
        dailyTasks.add(new DailyTask("TASK007", "Check Equipment", "Inspect grinders, brewers, and other equipment", "10 minutes", "medium"));
        dailyTasks.add(new DailyTask("TASK008", "Count Register", "Count cash drawer and record daily sales", "15 minutes", "high"));

    }

    // Daily Tasks methods
    public List<DailyTask> getDailyTasks() {
        return new ArrayList<>(dailyTasks);
    }

    public void toggleTaskCompletion(String taskId) {
        for (DailyTask task : dailyTasks) {
            if (task.getId().equals(taskId)) {
                task.setCompleted(!task.isCompleted());
                break;
            }
        }
    }

    public int getCompletedTasksCount() {
        int count = 0;
        for (DailyTask task : dailyTasks) {
            if (task.isCompleted()) {
                count++;
            }
        }
        return count;
    }

    public int getPendingTasksCount() {
        int count = 0;
        for (DailyTask task : dailyTasks) {
            if (!task.isCompleted()) {
                count++;
            }
        }
        return count;
    }

    public int getDeliveredOrdersToday() {
        // For demo purposes, return a mock number
        return 25 + (int)(Math.random() * 15);
    }

    public void markOrderAsDelivered(int orderId) {
        Order order = findOrderById(orderId);
        if (order != null) {
            order.setStatus("delivered");
        }
    }

    public List<InventoryItem> getInventoryItems() {
        return new ArrayList<>(inventoryItems);
    }

    public List<Order> getOrders() {
        return new ArrayList<>(orders);
    }

    // Recipe management methods
    private void initializeSampleRecipes() {
        Date now = new Date();

        // Sample recipes for drinks
        recipes.add(new Recipe(1, "Espresso", "Rich and bold single shot espresso", now, Arrays.asList(
                new RecipeStep(1, 1, "Grind coffee beans to a fine consistency", 10),
                new RecipeStep(2, 1, "Tamp the coffee grounds into the portafilter", 5),
                new RecipeStep(3, 1, "Brew the espresso shot", 25)
        )));

        recipes.add(new Recipe(2, "Cappuccino", "Espresso with steamed milk and thick foam", now, Arrays.asList(
                new RecipeStep(1, 2, "Grind coffee beans to a fine consistency", 10),
                new RecipeStep(2, 2, "Tamp the coffee grounds into the portafilter", 5),
                new RecipeStep(3, 2, "Brew the espresso shot", 25),
                new RecipeStep(4, 2, "Steam milk to 150°F", 10),
                new RecipeStep(5, 2, "Froth milk to create microfoam", 10),
                new RecipeStep(6, 2, "Pour steamed milk over espresso", 10),
                new RecipeStep(7, 2, "Add foam on top", 5)
        )));

        recipes.add(new Recipe(3, "Latte", "Espresso with steamed milk and light foam", now, Arrays.asList(
                new RecipeStep(1, 3, "Grind coffee beans to a fine consistency", 10),
                new RecipeStep(2, 3, "Tamp the coffee grounds into the portafilter", 5),
                new RecipeStep(3, 3, "Brew the espresso shot", 25),
                new RecipeStep(4, 3, "Steam milk to 150°F", 10),
                new RecipeStep(5, 3, "Pour steamed milk over espresso", 10),
                new RecipeStep(6, 3, "Add a small amount of foam on top", 5)
        )));

        recipes.add(new Recipe(4, "Mocha", "Espresso with chocolate and steamed milk", now, Arrays.asList(
                new RecipeStep(1, 4, "Grind coffee beans to a fine consistency", 10),
                new RecipeStep(2, 4, "Tamp the coffee grounds into the portafilter", 5),
                new RecipeStep(3, 4, "Brew the espresso shot", 25),
                new RecipeStep(4, 4, "Steam milk to 150°F", 10),
                new RecipeStep(5, 4, "Add chocolate syrup to the cup", 5),
                new RecipeStep(6, 4, "Pour steamed milk over espresso and chocolate", 10),
                new RecipeStep(7, 4, "Top with whipped cream and chocolate shavings", 5)
        )));

        // Sample recipes for food items
        recipes.add(new Recipe(5, "Croissant", "Buttery, flaky French pastry", now, Arrays.asList(
                new RecipeStep(1, 5, "Prepare the dough with flour, butter, and yeast", 120),
                new RecipeStep(2, 5, "Roll out the dough and fold in butter layers", 30),
                new RecipeStep(3, 5, "Cut and shape the croissants", 15),
                new RecipeStep(4, 5, "Proof the croissants until doubled in size", 60),
                new RecipeStep(5, 5, "Bake in the oven at 375°F for 15 minutes", 15)
        )));

        recipes.add(new Recipe(6, "Banana Bread", "Moist banana bread with walnuts", now, Arrays.asList(
                new RecipeStep(1, 6, "Preheat the oven to 350°F", 10),
                new RecipeStep(2, 6, "Mash ripe bananas in a bowl", 5),
                new RecipeStep(3, 6, "Mix in sugar, eggs, and melted butter", 10),
                new RecipeStep(4, 6, "Add flour, baking soda, and salt", 10),
                new RecipeStep(5, 6, "Fold in chopped walnuts", 5),
                new RecipeStep(6, 6, "Pour the batter into a greased loaf pan", 5),
                new RecipeStep(7, 6, "Bake for 60 minutes or until a toothpick comes out clean", 60)
        )));
    }

    public List<Recipe> getAllRecipes() {
        return new ArrayList<>(recipes);
    }

    public Recipe findRecipeById(int recipeId) {
        for (Recipe recipe : recipes) {
            if (recipe.getRecipeId() == recipeId) {
                return recipe;
            }
        }
        return null;
    }

    public List<Recipe> getRecipesByProductId(int productId) {
        List<Recipe> productRecipes = new ArrayList<>();
        for (Recipe recipe : recipes) {
            if (recipe.getProductId() == productId) {
                productRecipes.add(recipe);
            }
        }
        return productRecipes;
    }

    public List<Recipe> getRecipesByCategory(String category) {
        List<Recipe> filteredRecipes = new ArrayList<>();
        for (Recipe recipe : recipes) {
            if (recipe.getCategory().equalsIgnoreCase(category)) {
                filteredRecipes.add(recipe);
            }
        }
        return filteredRecipes;
    }

    public List<Recipe> searchRecipes(String query) {
        List<Recipe> searchResults = new ArrayList<>();
        for (Recipe recipe : recipes) {
            if (recipe.getName().toLowerCase().contains(query.toLowerCase()) ||
                recipe.getDescription().toLowerCase().contains(query.toLowerCase())) {
                searchResults.add(recipe);
            }
        }
        return searchResults;
    }

    // Additional methods for Recipe Guide
    public List<Recipe> getRecipes() {
        return getAllRecipes();
    }

    public Recipe getRecipeById(int recipeId) {
        return findRecipeById(recipeId);
    }
}
