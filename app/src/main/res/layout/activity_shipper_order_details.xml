<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="?android:colorBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal">

        <!-- Tiêu đề -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📦 Order Details"
            android:textAppearance="?attr/textAppearanceHeadline2"
            android:layout_marginBottom="16dp" />

        <!-- Thông tin đơn hàng -->
        <TextView
            android:id="@+id/tv_order_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Order details will appear here."
            android:textAppearance="?attr/textAppearanceBody1"
            android:padding="12dp"
            android:background="@android:color/white"
            android:elevation="2dp" />

        <!-- Nút chức năng -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_update_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Update Status"
            android:layout_marginTop="16dp"
            style="@style/Widget.CoffeeShop.Button" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_contact_customer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Contact Customer"
            android:layout_marginTop="12dp"
            style="@style/Widget.CoffeeShop.Button" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_view_map"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="View Map"
            android:layout_marginTop="12dp"
            style="@style/Widget.CoffeeShop.Button" />
    </LinearLayout>
</ScrollView>
