<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="4dp"
            app:title="Coffee Shop"
            app:titleTextColor="@color/white">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginEnd="16dp"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_cart"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cart"
                    android:textColor="@color/white" />

                <TextView
                    android:id="@+id/tv_cart_badge"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="-8dp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/circle_background"
                    android:text="0"
                    android:textColor="@color/white"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:visibility="gone" />

            </LinearLayout>

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

            <!-- Welcome Message -->
            <TextView
                android:id="@+id/tv_welcome"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Welcome back!"
                android:textAppearance="?attr/textAppearanceHeadline5"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginBottom="16dp" />

            <!-- Current Order Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_current_order"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardBackgroundColor="@color/primary"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Current Order"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textStyle="bold"
                        android:textColor="@color/white"
                        android:layout_marginBottom="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_current_order_id"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Order #12345"
                                android:textAppearance="?attr/textAppearanceBody1"
                                android:textStyle="bold"
                                android:textColor="@color/white"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:id="@+id/tv_current_order_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Preparing..."
                                android:textAppearance="?attr/textAppearanceBody2"
                                android:textColor="@color/white" />

                        </LinearLayout>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_track_order"
                            style="@style/Widget.MaterialComponents.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Track"
                            android:textColor="@color/white"
                            android:backgroundTint="@android:color/transparent"
                            app:strokeColor="@color/white"
                            app:strokeWidth="1dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Search Bar -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_search"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Search for coffee, food, or snacks...">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Category Tabs -->
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_categories"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/surface"
                app:tabMode="scrollable"
                app:tabIndicatorColor="@color/primary"
                app:tabSelectedTextColor="@color/primary"
                app:tabTextColor="@color/text_secondary"
                android:layout_marginBottom="16dp" />

            <!-- Products Count and Sort -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <TextView
                    android:id="@+id/tv_products_count"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Available Products"
                    android:textAppearance="?attr/textAppearanceBody2"
                    android:textColor="@color/text_secondary" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_sort"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Sort"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- Products RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_products"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                android:clipToPadding="false"
                android:paddingBottom="80dp"
                tools:listitem="@layout/item_product" />        </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/surface"
        app:itemIconTint="@color/primary"
        app:itemTextColor="@color/primary"
        app:menu="@menu/customer_bottom_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
