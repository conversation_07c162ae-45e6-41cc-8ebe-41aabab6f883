package com.example.coffeeshop.adapters;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.models.Recipe;
import com.google.android.material.card.MaterialCardView;

import java.util.List;

public class RecipeAdapter extends RecyclerView.Adapter<RecipeAdapter.RecipeViewHolder> {

    private List<Recipe> recipes;
    private OnRecipeClickListener onRecipeClickListener;

    public interface OnRecipeClickListener {
        void onRecipeClick(Recipe recipe);
    }

    public RecipeAdapter(List<Recipe> recipes, OnRecipeClickListener listener) {
        this.recipes = recipes;
        this.onRecipeClickListener = listener;
    }

    @NonNull
    @Override
    public RecipeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_recipe, parent, false);
        return new RecipeViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecipeViewHolder holder, int position) {
        Recipe recipe = recipes.get(position);
        holder.bind(recipe);
    }

    @Override
    public int getItemCount() {
        return recipes.size();
    }

    class RecipeViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardRecipe;
        private TextView tvRecipeName, tvRecipeDescription, tvPreparationTime, tvDifficulty, tvCategory, tvStepCount;

        public RecipeViewHolder(@NonNull View itemView) {
            super(itemView);
            cardRecipe = itemView.findViewById(R.id.card_recipe);
            tvRecipeName = itemView.findViewById(R.id.tv_recipe_name);
            tvRecipeDescription = itemView.findViewById(R.id.tv_recipe_description);
            tvPreparationTime = itemView.findViewById(R.id.tv_preparation_time);
            tvDifficulty = itemView.findViewById(R.id.tv_difficulty);
            tvCategory = itemView.findViewById(R.id.tv_category);
            tvStepCount = itemView.findViewById(R.id.tv_step_count);
        }

        public void bind(Recipe recipe) {
            tvRecipeName.setText(recipe.getName());
            tvRecipeDescription.setText(recipe.getDescription());
            tvPreparationTime.setText(recipe.getFormattedTime());
            tvDifficulty.setText(recipe.getDifficulty());
            tvCategory.setText(recipe.getCategory());
            tvStepCount.setText(recipe.getStepCount() + " steps");

            // Set difficulty color
            try {
                tvDifficulty.setTextColor(Color.parseColor(recipe.getDifficultyColor()));
            } catch (IllegalArgumentException e) {
                tvDifficulty.setTextColor(Color.GRAY);
            }

            cardRecipe.setOnClickListener(v -> {
                if (onRecipeClickListener != null) {
                    onRecipeClickListener.onRecipeClick(recipe);
                }
            });
        }
    }
}
