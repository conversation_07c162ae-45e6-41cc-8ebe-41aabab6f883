package com.example.coffeeshop.activities;

import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.coffeeshop.adapters.PaymentFailureAdapter;
import com.example.coffeeshop.data.MockDataManagerSupport;
import com.example.coffeeshop.models.PaymentFailure;
import com.example.coffeeshop.R;
import java.util.List;

public class PaymentFailureActivity extends AppCompatActivity {
    private RecyclerView recyclerPaymentFailures;
    private int customerId;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_payment_failure);
        customerId = getIntent().getIntExtra("customerId", 1);
        recyclerPaymentFailures = findViewById(R.id.recyclerPaymentFailures);
        recyclerPaymentFailures.setLayoutManager(new LinearLayoutManager(this));
        List<PaymentFailure> failures = MockDataManagerSupport.getInstance().getPaymentFailuresForCustomer(customerId);
        final PaymentFailureAdapter[] adapter = new PaymentFailureAdapter[1];
        adapter[0] = new PaymentFailureAdapter(failures, pf -> {
            MockDataManagerSupport.getInstance().resolvePaymentFailure(pf.id);
            pf.status = "Resolved";
            adapter[0].notifyDataSetChanged();
        });
        recyclerPaymentFailures.setAdapter(adapter[0]);
    }
}
