<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Order Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/tv_order_id"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="#ORD001"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/tv_ready_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5 min ago"
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

        <!-- Customer Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_person"
                android:contentDescription="Customer" />

            <TextView
                android:id="@+id/tv_customer_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="John Doe"
                android:textSize="14sp"
                android:textColor="@color/text_primary"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- Order Items -->
        <TextView
            android:id="@+id/tv_order_items"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2x Cappuccino, 1x Croissant"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="16dp" />

        <!-- Action Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_mark_delivered"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Mark as Delivered"
            android:textColor="@color/white"
            app:backgroundTint="@android:color/holo_green_dark"
            app:cornerRadius="8dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
