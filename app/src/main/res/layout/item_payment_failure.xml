<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:clickable="false"
    android:focusable="false">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_failure_reason"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Reason: Card declined"
            android:textStyle="bold"
            android:textSize="16sp"
            android:textColor="#B00020" />

        <TextView
            android:id="@+id/tv_failure_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Status: Pending"
            android:textColor="#FFA000"
            android:layout_marginTop="4dp" />

        <TextView
            android:id="@+id/tv_failure_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Date: 2025-07-01 09:50"
            android:textColor="#666"
            android:layout_marginTop="4dp" />

        <Button
            android:id="@+id/btn_resolve_failure"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Resolve"
            android:layout_marginTop="12dp"
            android:backgroundTint="#8B4513"
            android:textColor="#FFF"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:clickable="true" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
