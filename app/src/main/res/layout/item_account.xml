<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    style="@style/Widget.CoffeeShop.ListItem">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- User Info Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="12dp">

            <!-- Name -->
            <TextView
                android:id="@+id/tv_user_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="?attr/textAppearanceSubtitle1"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:textSize="18sp"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginBottom="8dp"
                tools:text="John Doe" />

            <!-- Email -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:src="@android:drawable/ic_dialog_email"
                    android:layout_marginEnd="8dp"
                    android:alpha="0.7"
                    android:layout_gravity="center_vertical" />

                <TextView
                    android:id="@+id/tv_user_email"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textAppearance="?attr/textAppearanceBody2"
                    android:textColor="@android:color/darker_gray"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:text="<EMAIL>" />

            </LinearLayout>

            <!-- Phone -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:src="@android:drawable/ic_menu_call"
                    android:layout_marginEnd="8dp"
                    android:alpha="0.7"
                    android:layout_gravity="center_vertical" />

                <TextView
                    android:id="@+id/tv_user_phone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textAppearance="?attr/textAppearanceBody2"
                    android:textColor="@android:color/darker_gray"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:text="****** 567 8900" />

            </LinearLayout>

        </LinearLayout>

        <!-- Role and Status Chips -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_user_role"
                style="@style/Widget.MaterialComponents.Chip.Action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:textSize="12sp"
                app:chipMinHeight="32dp"
                app:chipBackgroundColor="@color/primary"
                app:chipIcon="@android:drawable/ic_menu_myplaces"
                app:chipIconTint="@color/white"
                android:textColor="@color/white"
                tools:text="Barista" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_user_status"
                style="@style/Widget.MaterialComponents.Chip.Action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                app:chipMinHeight="32dp"
                app:chipIcon="@android:drawable/ic_menu_info_details"
                tools:text="Active" />

        </LinearLayout>

        <!-- Action Buttons Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:weightSum="3">

            <!-- Edit Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit_user"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="6dp"
                android:text="Edit"
                android:textColor="@color/primary"
                android:textSize="12sp"
                app:icon="@android:drawable/ic_menu_edit"
                app:iconSize="16dp"
                app:iconTint="@color/primary"
                app:strokeColor="@color/primary"
                app:strokeWidth="1dp" />

            <!-- Activate/Deactivate Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_toggle_status"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="3dp"
                android:text="Activate"
                android:textColor="@android:color/holo_orange_dark"
                android:textSize="12sp"
                app:icon="@android:drawable/ic_menu_preferences"
                app:iconSize="16dp"
                app:iconTint="@android:color/holo_orange_dark"
                app:strokeColor="@android:color/holo_orange_dark"
                app:strokeWidth="1dp"
                tools:text="Deactivate" />

            <!-- Delete Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete_user"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginStart="6dp"
                android:text="Delete"
                android:textColor="@android:color/holo_red_dark"
                android:textSize="12sp"
                app:icon="@android:drawable/ic_menu_delete"
                app:iconSize="16dp"
                app:iconTint="@android:color/holo_red_dark"
                app:strokeColor="@android:color/holo_red_dark"
                app:strokeWidth="1dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>