<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Icon -->
        <ImageView
            android:id="@+id/iv_item_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/pill_background"
            android:padding="12dp"
            android:src="@drawable/ic_inventory"
            android:contentDescription="Item icon" />

        <!-- Item Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_item_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Coffee Beans"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/tv_item_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ingredients"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Stock Info -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <TextView
                android:id="@+id/tv_stock_quantity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/primary" />

            <TextView
                android:id="@+id/tv_stock_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="kg"
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

            <!-- Stock Status Badge -->
            <TextView
                android:id="@+id/tv_stock_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Low Stock"
                android:textSize="10sp"
                android:textColor="@android:color/white"
                android:background="@android:color/holo_orange_dark"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:layout_marginTop="4dp"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
