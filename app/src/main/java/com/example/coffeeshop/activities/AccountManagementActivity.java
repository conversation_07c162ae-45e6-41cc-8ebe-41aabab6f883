package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.textfield.TextInputEditText;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.AccountAdapter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class AccountManagementActivity extends AppCompatActivity implements AccountAdapter.OnAccountActionListener {

    private Toolbar toolbar;
    private TextInputEditText etSearch;
    private ChipGroup chipGroupRoles;
    private ChipGroup chipGroupStatus;
    private MaterialButton btnAddUser;
    private MaterialButton btnSort;
    private RecyclerView rvUsers;
    private View layoutEmptyState;

    private Chip chipAllRoles, chipBarista, chipManager, chipShipper, chipSupport;
    private Chip chipAllStatus, chipActive, chipInactive;

    private AccountAdapter accountAdapter;
    private List<Account> allAccounts;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_account_management);

        initViews();
        setupToolbar();
        setupRecyclerView();
        setupClickListeners();
        setupSearchFilter();
        setupChipFilters();
        loadMockData();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        etSearch = findViewById(R.id.et_search);
        chipGroupRoles = findViewById(R.id.chip_group_roles);
        chipGroupStatus = findViewById(R.id.chip_group_status);
        btnAddUser = findViewById(R.id.btn_add_user);
        btnSort = findViewById(R.id.btn_sort);
        rvUsers = findViewById(R.id.rv_users);
        layoutEmptyState = findViewById(R.id.layout_empty_state);

        chipAllRoles = findViewById(R.id.chip_all_roles);
        chipBarista = findViewById(R.id.chip_barista);
        chipManager = findViewById(R.id.chip_manager);
        chipShipper = findViewById(R.id.chip_shipper);
        chipSupport = findViewById(R.id.chip_support);

        chipAllStatus = findViewById(R.id.chip_all_status);
        chipActive = findViewById(R.id.chip_active);
        chipInactive = findViewById(R.id.chip_inactive);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Account Management");
        }
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
    }

    private void setupRecyclerView() {
        rvUsers.setLayoutManager(new LinearLayoutManager(this));
        accountAdapter = new AccountAdapter(this, this);
        rvUsers.setAdapter(accountAdapter);
    }

    private void setupClickListeners() {
        btnAddUser.setOnClickListener(v -> openAddAccountActivity());
        btnSort.setOnClickListener(v -> showSortDialog());
    }

    private void setupSearchFilter() {
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            @Override public void onTextChanged(CharSequence s, int start, int before, int count) {}
            @Override public void afterTextChanged(Editable s) {
                filterUsers();
            }
        });
    }

    private void setupChipFilters() {
        chipGroupRoles.setOnCheckedStateChangeListener((group, checkedIds) -> {
            handleRoleChipSelection(checkedIds);
            filterUsers();
        });

        chipGroupStatus.setOnCheckedStateChangeListener((group, checkedIds) -> {
            handleStatusChipSelection(checkedIds);
            filterUsers();
        });
    }

    private void handleRoleChipSelection(List<Integer> checkedIds) {
        boolean allSelected = checkedIds.contains(R.id.chip_all_roles);
        boolean specificSelected = checkedIds.contains(R.id.chip_barista) ||
                checkedIds.contains(R.id.chip_manager) ||
                checkedIds.contains(R.id.chip_shipper) ||
                checkedIds.contains(R.id.chip_support);

        if (allSelected && specificSelected) {
            chipBarista.setChecked(false);
            chipManager.setChecked(false);
            chipShipper.setChecked(false);
            chipSupport.setChecked(false);
        } else if (!allSelected && specificSelected && chipAllRoles.isChecked()) {
            chipAllRoles.setChecked(false);
        } else if (checkedIds.isEmpty()) {
            chipAllRoles.setChecked(true);
        }
    }

    private void handleStatusChipSelection(List<Integer> checkedIds) {
        boolean allSelected = checkedIds.contains(R.id.chip_all_status);
        boolean specificSelected = checkedIds.contains(R.id.chip_active) ||
                checkedIds.contains(R.id.chip_inactive);

        if (allSelected && specificSelected) {
            chipActive.setChecked(false);
            chipInactive.setChecked(false);
        } else if (!allSelected && specificSelected && chipAllStatus.isChecked()) {
            chipAllStatus.setChecked(false);
        } else if (checkedIds.isEmpty()) {
            chipAllStatus.setChecked(true);
        }
    }

    private Set<String> getSelectedRoles() {
        Set<String> roles = new HashSet<>();
        if (chipAllRoles.isChecked()) {
            roles.addAll(Arrays.asList("Barista", "Manager", "Shipper", "Support"));
        } else {
            if (chipBarista.isChecked()) roles.add("Barista");
            if (chipManager.isChecked()) roles.add("Manager");
            if (chipShipper.isChecked()) roles.add("Shipper");
            if (chipSupport.isChecked()) roles.add("Support");
        }
        return roles;
    }

    private Set<String> getSelectedStatuses() {
        Set<String> statuses = new HashSet<>();
        if (chipAllStatus.isChecked()) {
            statuses.addAll(Arrays.asList("Active", "Inactive"));
        } else {
            if (chipActive.isChecked()) statuses.add("Active");
            if (chipInactive.isChecked()) statuses.add("Inactive");
        }
        return statuses;
    }

    private void filterUsers() {
        String searchQuery = etSearch.getText().toString().trim().toLowerCase();
        Set<String> selectedRoles = getSelectedRoles();
        Set<String> selectedStatuses = getSelectedStatuses();

        List<Account> filteredAccounts = new ArrayList<>();

        for (Account account : allAccounts) {
            boolean matchesSearch = searchQuery.isEmpty() ||
                    account.getUsername().toLowerCase().contains(searchQuery) ||
                    account.getEmail().toLowerCase().contains(searchQuery) ||
                    account.getPhone().toLowerCase().contains(searchQuery);

            boolean matchesRole = selectedRoles.contains(account.getRole());
            boolean matchesStatus = selectedStatuses.contains(account.getStatus());

            if (matchesSearch && matchesRole && matchesStatus) {
                filteredAccounts.add(account);
            }
        }

        accountAdapter.updateList(filteredAccounts);
        if (filteredAccounts.isEmpty()) {
            showEmptyState();
        } else {
            hideEmptyState();
        }
    }

    private void openAddAccountActivity() {
        Intent intent = new Intent(this, AddAccountActivity.class);
        startActivity(intent);
    }

    private void showSortDialog() {
        String[] sortOptions = {"Name A-Z", "Name Z-A", "Email A-Z", "Role", "Status", "Date Created"};

        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Sort By")
                .setItems(sortOptions, (dialog, which) -> sortAccounts(which))
                .show();
    }

    private void sortAccounts(int sortType) {
        List<Account> currentList = new ArrayList<>(allAccounts);
        switch (sortType) {
            case 0: Collections.sort(currentList, (a, b) -> a.getUsername().compareToIgnoreCase(b.getUsername())); break;
            case 1: Collections.sort(currentList, (a, b) -> b.getUsername().compareToIgnoreCase(a.getUsername())); break;
            case 2: Collections.sort(currentList, (a, b) -> a.getEmail().compareToIgnoreCase(b.getEmail())); break;
            case 3: Collections.sort(currentList, (a, b) -> a.getRole().compareToIgnoreCase(b.getRole())); break;
            case 4: Collections.sort(currentList, (a, b) -> a.getStatus().compareToIgnoreCase(b.getStatus())); break;
            case 5: Collections.sort(currentList, (a, b) -> a.getDateCreated().compareToIgnoreCase(b.getDateCreated())); break;
        }
        allAccounts = currentList;
        filterUsers();
    }

    private void showEmptyState() {
        rvUsers.setVisibility(View.GONE);
        layoutEmptyState.setVisibility(View.VISIBLE);
    }

    private void hideEmptyState() {
        rvUsers.setVisibility(View.VISIBLE);
        layoutEmptyState.setVisibility(View.GONE);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (allAccounts == null || allAccounts.isEmpty()) {
            loadMockData();
        }
    }

    private void loadMockData() {
        allAccounts = new ArrayList<>();
        allAccounts.add(new Account("john_doe", "<EMAIL>", "+**********", "Barista", "Active", "2024-01-15"));
        allAccounts.add(new Account("jane_smith", "<EMAIL>", "+**********", "Manager", "Active", "2024-01-10"));
        allAccounts.add(new Account("mike_wilson", "<EMAIL>", "+**********", "Shipper", "Active", "2024-01-20"));
        allAccounts.add(new Account("sarah_johnson", "<EMAIL>", "+**********", "Support", "Inactive", "2024-01-12"));
        allAccounts.add(new Account("david_brown", "<EMAIL>", "+**********", "Barista", "Active", "2024-01-18"));
        allAccounts.add(new Account("lisa_davis", "<EMAIL>", "+**********", "Shipper", "Inactive", "2024-01-08"));
        allAccounts.add(new Account("robert_miller", "<EMAIL>", "+**********", "Support", "Active", "2024-01-25"));
        allAccounts.add(new Account("emily_garcia", "<EMAIL>", "+**********", "Barista", "Active", "2024-01-22"));
        filterUsers();
    }

    @Override
    public void onAccountDeleted(Account account, int position) {
        allAccounts.remove(account);
        filterUsers();
    }

    @Override
    public void onAccountEdit(Account account) {
        Intent intent = new Intent(this, EditAccountActivity.class);
        intent.putExtra("username", account.getUsername());
        intent.putExtra("email", account.getEmail());
        intent.putExtra("phone", account.getPhone());
        intent.putExtra("role", account.getRole());
        intent.putExtra("status", account.getStatus());
        intent.putExtra("dateCreated", account.getDateCreated());
        startActivity(intent);
    }

    @Override
    public void onAccountStatusToggled(Account account, int position) {
        filterUsers();
    }

    public static class Account {
        private String username, email, phone, role, status, dateCreated;

        public Account(String username, String email, String phone, String role, String status, String dateCreated) {
            this.username = username;
            this.email = email;
            this.phone = phone;
            this.role = role;
            this.status = status;
            this.dateCreated = dateCreated;
        }

        public String getUsername() { return username; }
        public String getEmail() { return email; }
        public String getPhone() { return phone; }
        public String getRole() { return role; }
        public String getStatus() { return status; }
        public String getDateCreated() { return dateCreated; }

        public void setUsername(String username) { this.username = username; }
        public void setEmail(String email) { this.email = email; }
        public void setPhone(String phone) { this.phone = phone; }
        public void setRole(String role) { this.role = role; }
        public void setStatus(String status) { this.status = status; }
        public void setDateCreated(String dateCreated) { this.dateCreated = dateCreated; }
    }
}
