package com.example.coffeeshop.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.example.coffeeshop.R;
import com.example.coffeeshop.models.CartItem;

import java.math.BigDecimal;
import java.util.List;

public class CheckoutItemAdapter extends RecyclerView.Adapter<CheckoutItemAdapter.ViewHolder> {

    private List<CartItem> cartItems;
    private OnCartItemActionListener listener;

    public interface OnCartItemActionListener {
        void onQuantityChanged(CartItem cartItem, int newQuantity);
        void onItemRemoved(CartItem cartItem);
    }

    public CheckoutItemAdapter(List<CartItem> cartItems) {
        this.cartItems = cartItems;
    }

    public void setOnCartItemActionListener(OnCartItemActionListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_checkout, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        CartItem cartItem = cartItems.get(position);
        
        // Use the product data from the CartItem itself (which came from API)
        if (cartItem.getProduct() != null) {
            holder.tvProductName.setText(cartItem.getProduct().getName());
        } else {
            holder.tvProductName.setText("Unknown Product");
        }
        
        holder.tvQuantity.setText(String.valueOf(cartItem.getQuantity()));
        
        BigDecimal itemTotal = cartItem.getUnitPrice().multiply(new BigDecimal(cartItem.getQuantity()));
        holder.tvPrice.setText("$" + itemTotal.setScale(2, BigDecimal.ROUND_HALF_UP));
        holder.tvUnitPrice.setText("$" + cartItem.getUnitPrice().setScale(2, BigDecimal.ROUND_HALF_UP) + " each");
        
        // Load product image from CartItem's product
        if (cartItem.getProduct() != null) {
            Glide.with(holder.itemView.getContext())
                    .load(cartItem.getProduct().getImageUrl())
                    .placeholder(R.drawable.placeholder_coffee)
                    .error(R.drawable.placeholder_coffee)
                    .into(holder.ivProductImage);
        }

        // Set up quantity controls
        holder.btnDecreaseQuantity.setOnClickListener(v -> {
            if (cartItem.getQuantity() > 1 && listener != null) {
                listener.onQuantityChanged(cartItem, cartItem.getQuantity() - 1);
            }
        });

        holder.btnIncreaseQuantity.setOnClickListener(v -> {
            if (listener != null) {
                listener.onQuantityChanged(cartItem, cartItem.getQuantity() + 1);
            }
        });

        holder.btnRemoveItem.setOnClickListener(v -> {
            if (listener != null) {
                listener.onItemRemoved(cartItem);
            }
        });
    }

    @Override
    public int getItemCount() {
        return cartItems.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivProductImage;
        TextView tvProductName, tvQuantity, tvPrice, tvUnitPrice;
        Button btnDecreaseQuantity, btnIncreaseQuantity;
        ImageButton btnRemoveItem;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivProductImage = itemView.findViewById(R.id.iv_product_image);
            tvProductName = itemView.findViewById(R.id.tv_product_name);
            tvQuantity = itemView.findViewById(R.id.tv_quantity);
            tvPrice = itemView.findViewById(R.id.tv_price);
            tvUnitPrice = itemView.findViewById(R.id.tv_unit_price);
            btnDecreaseQuantity = itemView.findViewById(R.id.btn_decrease_quantity);
            btnIncreaseQuantity = itemView.findViewById(R.id.btn_increase_quantity);
            btnRemoveItem = itemView.findViewById(R.id.btn_remove_item);
        }
    }
}
