package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.DailyTaskAdapter;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.DailyTask;
import java.util.List;
import java.util.stream.Collectors;

public class BaristaDailyTasksActivity extends AppCompatActivity {
    private RecyclerView rvTasks;
    private DailyTaskAdapter taskAdapter;
    private SwipeRefreshLayout swipeRefresh;
    private ChipGroup chipGroupFilter;
    private TextView tvCompletedTasks, tvPendingTasks, tvProgressText;
    private LinearProgressIndicator progressBar;
    
    private List<DailyTask> allTasks;
    private String currentFilter = "all";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_barista_daily_tasks);

        initViews();
        setupBottomNavigation();
        setupRecyclerView();
        setupFilters();
        loadTasks();
    }

    private void initViews() {
        rvTasks = findViewById(R.id.rv_tasks);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        chipGroupFilter = findViewById(R.id.chip_group_filter);
        tvCompletedTasks = findViewById(R.id.tv_completed_tasks);
        tvPendingTasks = findViewById(R.id.tv_pending_tasks);
        tvProgressText = findViewById(R.id.tv_progress_text);
        progressBar = findViewById(R.id.progress_bar);

        swipeRefresh.setOnRefreshListener(this::loadTasks);
    }

    private void setupBottomNavigation() {
        BottomNavigationView bottomNav = findViewById(R.id.bottom_navigation);
        bottomNav.setSelectedItemId(R.id.nav_dashboard); // Default to dashboard since tasks is no longer in menu
        bottomNav.setOnNavigationItemSelectedListener(new BottomNavigationView.OnNavigationItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_dashboard) {
                    startActivity(new Intent(BaristaDailyTasksActivity.this, BaristaDashboardActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    startActivity(new Intent(BaristaDailyTasksActivity.this, BaristaOrderQueueActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_recipes) {
                    startActivity(new Intent(BaristaDailyTasksActivity.this, BaristaRecipeGuideActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_inventory) {
                    startActivity(new Intent(BaristaDailyTasksActivity.this, BaristaInventoryActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_ready) {
                    startActivity(new Intent(BaristaDailyTasksActivity.this, BaristaReadyOrdersActivity.class));
                    finish();
                    return true;
                }
                return false;
            }
        });
    }

    private void setupRecyclerView() {
        taskAdapter = new DailyTaskAdapter(this::onTaskToggled);
        rvTasks.setLayoutManager(new LinearLayoutManager(this));
        rvTasks.setAdapter(taskAdapter);
    }

    private void setupFilters() {
        chipGroupFilter.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.chip_all_tasks) {
                currentFilter = "all";
            } else if (checkedId == R.id.chip_pending) {
                currentFilter = "pending";
            } else if (checkedId == R.id.chip_completed) {
                currentFilter = "completed";
            }
            filterTasks();
        });
    }

    private void loadTasks() {
        allTasks = MockDataManager.getInstance().getDailyTasks();
        updateProgress();
        filterTasks();
        swipeRefresh.setRefreshing(false);
    }

    private void filterTasks() {
        List<DailyTask> filteredTasks;
        
        switch (currentFilter) {
            case "pending":
                filteredTasks = allTasks.stream()
                    .filter(task -> !task.isCompleted())
                    .collect(Collectors.toList());
                break;
            case "completed":
                filteredTasks = allTasks.stream()
                    .filter(DailyTask::isCompleted)
                    .collect(Collectors.toList());
                break;
            default:
                filteredTasks = allTasks;
                break;
        }
        
        taskAdapter.updateTasks(filteredTasks);
    }

    private void updateProgress() {
        int completedCount = MockDataManager.getInstance().getCompletedTasksCount();
        int pendingCount = MockDataManager.getInstance().getPendingTasksCount();
        int totalTasks = completedCount + pendingCount;
        
        tvCompletedTasks.setText(String.valueOf(completedCount));
        tvPendingTasks.setText(String.valueOf(pendingCount));
        
        if (totalTasks > 0) {
            int progressPercent = (completedCount * 100) / totalTasks;
            progressBar.setProgress(progressPercent);
            tvProgressText.setText(progressPercent + "% Complete");
        } else {
            progressBar.setProgress(0);
            tvProgressText.setText("0% Complete");
        }
    }

    private void onTaskToggled(DailyTask task) {
        MockDataManager.getInstance().toggleTaskCompletion(task.getId());
        // Use post to delay the refresh until after the current layout pass
        new android.os.Handler(android.os.Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                loadTasks(); // Refresh to update progress
            }
        });
    }
}
