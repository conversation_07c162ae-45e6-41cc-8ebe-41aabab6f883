package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;
import com.google.android.material.button.MaterialButton;

public class ShipperUpdateOrderStatusActivity extends AppCompatActivity {

    private Spinner spinnerStatus;
    private MaterialButton btnConfirm;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_update_status);

        spinnerStatus = findViewById(R.id.spinner_status);
        btnConfirm = findViewById(R.id.btn_confirm_update);

        // Dữ liệu trạng thái giả lập
        String[] statuses = {"In Progress", "Delivered", "Failed", "Returned"};

        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, statuses);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerStatus.setAdapter(adapter);

        // Sự kiện khi nhấn nút
        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String selectedStatus = spinnerStatus.getSelectedItem().toString();
                Toast.makeText(ShipperUpdateOrderStatusActivity.this, "Status updated to: " + selectedStatus, Toast.LENGTH_SHORT).show();
                finish(); // Quay lại màn trước
            }
        });
    }
}
