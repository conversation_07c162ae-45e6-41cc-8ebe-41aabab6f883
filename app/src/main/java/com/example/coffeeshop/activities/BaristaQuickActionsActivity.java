package com.example.coffeeshop.activities;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.example.coffeeshop.R;
import com.example.coffeeshop.data.MockDataManager;

public class BaristaQuickActionsActivity extends AppCompatActivity {
    private TextView tvOrdersToday, tvTasksCompleted, tvLowStockItems;
    private MaterialCardView cardViewOrders, cardCheckInventory, cardDailyTasks, cardReadyOrders;
    private MaterialButton btnCallManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_barista_quick_actions);

        initViews();
        setupBottomNavigation();
        setupClickListeners();
        loadStats();
    }

    private void initViews() {
        tvOrdersToday = findViewById(R.id.tv_orders_today);
        tvTasksCompleted = findViewById(R.id.tv_tasks_completed);
        tvLowStockItems = findViewById(R.id.tv_low_stock_items);
        
        cardViewOrders = findViewById(R.id.card_view_orders);
        cardCheckInventory = findViewById(R.id.card_check_inventory);
        cardDailyTasks = findViewById(R.id.card_daily_tasks);
        cardReadyOrders = findViewById(R.id.card_ready_orders);
        
        btnCallManager = findViewById(R.id.btn_call_manager);
    }

    private void setupBottomNavigation() {
        BottomNavigationView bottomNav = findViewById(R.id.bottom_navigation);
        bottomNav.setSelectedItemId(R.id.nav_dashboard); // Quick Actions uses dashboard icon
        bottomNav.setOnNavigationItemSelectedListener(new BottomNavigationView.OnNavigationItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_dashboard) {
                    startActivity(new Intent(BaristaQuickActionsActivity.this, BaristaDashboardActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    startActivity(new Intent(BaristaQuickActionsActivity.this, BaristaOrderQueueActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_recipes) {
                    startActivity(new Intent(BaristaQuickActionsActivity.this, BaristaRecipeGuideActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_inventory) {
                    startActivity(new Intent(BaristaQuickActionsActivity.this, BaristaInventoryActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_ready) {
                    startActivity(new Intent(BaristaQuickActionsActivity.this, BaristaReadyOrdersActivity.class));
                    finish();
                    return true;
                }
                return false;
            }
        });
    }

    private void setupClickListeners() {
        cardViewOrders.setOnClickListener(v -> {
            startActivity(new Intent(this, BaristaOrderQueueActivity.class));
        });

        cardCheckInventory.setOnClickListener(v -> {
            startActivity(new Intent(this, BaristaInventoryActivity.class));
        });

        cardDailyTasks.setOnClickListener(v -> {
            startActivity(new Intent(this, BaristaDailyTasksActivity.class));
        });

        cardReadyOrders.setOnClickListener(v -> {
            startActivity(new Intent(this, BaristaReadyOrdersActivity.class));
        });

        btnCallManager.setOnClickListener(v -> {
            // Simulate calling manager
            Intent dialIntent = new Intent(Intent.ACTION_DIAL);
            dialIntent.setData(Uri.parse("tel:+1234567890"));
            try {
                startActivity(dialIntent);
            } catch (Exception e) {
                Toast.makeText(this, "Unable to make call", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void loadStats() {
        MockDataManager dataManager = MockDataManager.getInstance();
        
        // Today's orders count
        int ordersToday = dataManager.getTodayCompletedOrderCount();
        tvOrdersToday.setText(String.valueOf(ordersToday));
        
        // Tasks completion
        int completedTasks = dataManager.getCompletedTasksCount();
        int totalTasks = completedTasks + dataManager.getPendingTasksCount();
        tvTasksCompleted.setText(completedTasks + "/" + totalTasks);
        
        // Low stock items count
        long lowStockCount = dataManager.getInventoryItems().stream()
            .filter(item -> item.getQuantity() > 0 && item.getQuantity() <= item.getMinStock())
            .count();
        tvLowStockItems.setText(String.valueOf(lowStockCount));
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadStats(); // Refresh stats when returning to this screen
    }
}
