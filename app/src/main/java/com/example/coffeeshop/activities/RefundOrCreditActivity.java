package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.RadioGroup;
import android.widget.RadioButton;
import android.widget.CheckBox;
import android.widget.Toast;
import android.view.View;
import android.widget.ArrayAdapter;
import androidx.appcompat.app.AppCompatActivity;
import com.example.coffeeshop.data.MockDataManagerSupport;
import com.example.coffeeshop.R;

public class RefundOrCreditActivity extends AppCompatActivity {
    private Spinner spinnerOrderId, spinnerPaymentMethod;
    private RadioGroup radioGroupType;
    private RadioButton radioRefund, radioCredit;
    private EditText editAmount, editReason;
    private CheckBox checkboxConfirm;
    private Button btnProcess, btnCancel;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_refund_or_credit);
        spinnerOrderId = findViewById(R.id.spinnerOrderId);
        spinnerPaymentMethod = findViewById(R.id.spinnerPaymentMethod);
        radioGroupType = findViewById(R.id.radioGroupType);
        radioRefund = findViewById(R.id.radioRefund);
        radioCredit = findViewById(R.id.radioCredit);
        editAmount = findViewById(R.id.editAmount);
        editReason = findViewById(R.id.editReason);
        checkboxConfirm = findViewById(R.id.checkboxConfirm);
        btnProcess = findViewById(R.id.btnProcess);
        btnCancel = findViewById(R.id.btnCancel);

        // Mock data for order IDs and payment methods
        String[] orderIds = {"101", "102", "103", "104"};
        String[] paymentMethods = {"Credit Card", "Cash", "E-Wallet"};
        spinnerOrderId.setAdapter(new ArrayAdapter<>(this, android.R.layout.simple_spinner_dropdown_item, orderIds));
        spinnerPaymentMethod.setAdapter(new ArrayAdapter<>(this, android.R.layout.simple_spinner_dropdown_item, paymentMethods));

        // If launched with a ticket/order context, pre-select the related order
        int currentTicketId = getIntent().getIntExtra("ticketId", -1);
        int currentCustomerId = getIntent().getIntExtra("customerId", -1);
        String currentOrderId = getIntent().hasExtra("orderId") ? getIntent().getStringExtra("orderId") : null;
        if (currentOrderId != null) {
            for (int i = 0; i < orderIds.length; i++) {
                if (orderIds[i].equals(currentOrderId)) {
                    spinnerOrderId.setSelection(i);
                    break;
                }
            }
        }

        // Validation logic
        View.OnClickListener validateListener = v -> validateForm();
        editAmount.setOnFocusChangeListener((v, hasFocus) -> validateForm());
        editReason.setOnFocusChangeListener((v, hasFocus) -> validateForm());
        checkboxConfirm.setOnCheckedChangeListener((buttonView, isChecked) -> validateForm());
        radioGroupType.setOnCheckedChangeListener((group, checkedId) -> validateForm());
        spinnerOrderId.setOnItemSelectedListener(new android.widget.AdapterView.OnItemSelectedListener() {
            @Override public void onItemSelected(android.widget.AdapterView<?> parent, View view, int position, long id) { validateForm(); }
            @Override public void onNothingSelected(android.widget.AdapterView<?> parent) {}
        });
        spinnerPaymentMethod.setOnItemSelectedListener(new android.widget.AdapterView.OnItemSelectedListener() {
            @Override public void onItemSelected(android.widget.AdapterView<?> parent, View view, int position, long id) { validateForm(); }
            @Override public void onNothingSelected(android.widget.AdapterView<?> parent) {}
        });

        btnProcess.setOnClickListener(v -> {
            if (validateForm()) {
                Toast.makeText(this, "Refund/Credit processed!", Toast.LENGTH_SHORT).show();
                finish();
            }
        });
        btnCancel.setOnClickListener(v -> finish());
        validateForm();
    }
    private boolean validateForm() {
        boolean valid = true;
        if (spinnerOrderId.getSelectedItem() == null) valid = false;
        if (radioGroupType.getCheckedRadioButtonId() == -1) valid = false;
        String amount = editAmount.getText().toString();
        if (amount.isEmpty() || Double.parseDouble(amount) <= 0) valid = false;
        String reason = editReason.getText().toString();
        if (reason.isEmpty() || reason.length() > 500) valid = false;
        if (spinnerPaymentMethod.getSelectedItem() == null) valid = false;
        if (!checkboxConfirm.isChecked()) valid = false;
        btnProcess.setEnabled(valid);
        return valid;
    }
}
