package com.example.coffeeshop.activities;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import com.example.coffeeshop.R;

public class EscalateTicketActivity extends AppCompatActivity {
    private static final int PICK_ATTACHMENT_REQUEST = 1001;
    private EditText editEscalationReason, editRecommendedAction;
    private TextView textCurrentAssignee, textAttachmentInfo;
    private Button btnAddAttachment, btnConfirmEscalation, btnCancelEscalation;
    private Uri attachmentUri = null;
    private String assigneeName = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_escalate_ticket);

        editEscalationReason = findViewById(R.id.editEscalationReason);
        editRecommendedAction = findViewById(R.id.editRecommendedAction);
        textCurrentAssignee = findViewById(R.id.textCurrentAssignee);
        textAttachmentInfo = findViewById(R.id.textAttachmentInfo);
        btnAddAttachment = findViewById(R.id.btnAddAttachment);
        btnConfirmEscalation = findViewById(R.id.btnConfirmEscalation);
        btnCancelEscalation = findViewById(R.id.btnCancelEscalation);

        // Mock: get assignee from intent or use default
        assigneeName = getIntent().getStringExtra("assigneeName");
        if (TextUtils.isEmpty(assigneeName)) assigneeName = "Staff Member";
        textCurrentAssignee.setText(assigneeName);

        btnAddAttachment.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
            intent.setType("*/*");
            startActivityForResult(Intent.createChooser(intent, "Select Attachment"), PICK_ATTACHMENT_REQUEST);
        });

        btnCancelEscalation.setOnClickListener(v -> finish());

        btnConfirmEscalation.setOnClickListener(v -> {
            String reason = editEscalationReason.getText().toString().trim();
            if (TextUtils.isEmpty(reason)) {
                editEscalationReason.setError("Escalation reason is required");
                editEscalationReason.requestFocus();
                return;
            }
            // Mock: Show a toast and finish
            Toast.makeText(this, "Escalation submitted!", Toast.LENGTH_LONG).show();
            setResult(Activity.RESULT_OK);
            finish();
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == PICK_ATTACHMENT_REQUEST && resultCode == RESULT_OK && data != null) {
            attachmentUri = data.getData();
            if (attachmentUri != null) {
                textAttachmentInfo.setText("Selected: " + attachmentUri.getLastPathSegment());
            } else {
                textAttachmentInfo.setText("No file selected");
            }
        }
    }
}
