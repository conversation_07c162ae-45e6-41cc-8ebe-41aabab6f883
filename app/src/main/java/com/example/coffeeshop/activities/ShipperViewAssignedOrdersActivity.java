package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;

public class ShipperViewAssignedOrdersActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_view_assigned_orders);

        // Bắt sự kiện nút Details của đơn hàng ORD001
        Button btnDetails1 = findViewById(R.id.btn_details);
        btnDetails1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(ShipperViewAssignedOrdersActivity.this, ShipperOrderDetailsActivity.class);
                // Tạm thời truyền dữ liệu ảo nếu cần
                intent.putExtra("orderId", "ORD001");
                intent.putExtra("customer", "John Doe");
                intent.putExtra("address", "123 Main Street");
                intent.putExtra("status", "Accepted");
                startActivity(intent);
            }
        });

        // Bắt sự kiện nút Details của đơn hàng ORD002
        Button btnDetails2 = findViewById(R.id.btn_details_2);
        btnDetails2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(ShipperViewAssignedOrdersActivity.this, ShipperOrderDetailsActivity.class);
                intent.putExtra("orderId", "ORD002");
                intent.putExtra("customer", "Alice Smith");
                intent.putExtra("address", "456 Coffee Lane");
                intent.putExtra("status", "Rejected");
                startActivity(intent);
            }
        });
    }
}
