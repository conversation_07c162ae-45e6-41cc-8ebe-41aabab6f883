<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">
    <TextView android:id="@+id/textSubject" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Subject" android:textStyle="bold"/>
    <TextView android:id="@+id/textDescription" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Description"/>
    <TextView android:id="@+id/textStatus" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Status"/>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewChat"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:overScrollMode="always"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <EditText android:id="@+id/editChatMessage" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_weight="1" android:hint="Type a message..."/>
        <Button android:id="@+id/btnSendMessage" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Send"/>
    </LinearLayout>
    <Button android:id="@+id/btnEscalate" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="Escalate"/>
    <Button android:id="@+id/btnUpdateStatus" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="Mark as Resolved"/>
    <Button android:id="@+id/btnOrderHistory" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="View Order History"/>
    <Button android:id="@+id/btnRefund" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="Process Refund"/>
    <Button android:id="@+id/btnPaymentFailure" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="Handle Payment Failure"/>
</LinearLayout>
