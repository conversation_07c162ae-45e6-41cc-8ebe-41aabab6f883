<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Coffee shop theme colors -->
    <color name="primary">#6F4E37</color><!-- Coffee brown -->
    <color name="primary_dark">#4A3728</color><!-- Dark coffee brown -->
    <color name="primary_light">#9C7A5B</color><!-- Light coffee brown -->
    <color name="accent">#D4A76A</color><!-- Caramel accent -->
    <color name="secondary">#B87333</color><!-- Copper brown -->    <!-- Neutral colors -->
    <color name="background">#FFFBF7</color><!-- Cream white -->
    <color name="surface">#FFF8EE</color><!-- Light cream -->
    <color name="background_night">#FFFBF7</color><!-- Default to light background -->
    <color name="surface_night">#FFF8EE</color><!-- Default to light surface -->
    
    <color name="text_primary">#332621</color>
    <color name="text_secondary">#7D6E67</color><!-- Medium brown text -->
    <color name="text_primary_night">#332621</color><!-- Default to dark text for light mode -->
    <color name="text_secondary_night">#7D6E67</color><!-- Default to dark text for light mode -->

    <!-- Status colors -->
    <color name="success">#43A047</color><!-- Green -->
    <color name="error">#D32F2F</color><!-- Red -->
    <color name="warning">#FFA000</color><!-- Amber -->
    <color name="info">#1976D2</color><!-- Blue -->

    <!-- Aliases for compatibility -->
    <color name="primary_color">@color/primary</color>
    <color name="accent_color">@color/accent</color>
    <color name="background_color">@color/background</color>
    <color name="step_background">@color/surface</color>
    <color name="bottom_nav_color">@android:color/white</color>

    <!-- Legacy colors (kept for compatibility) -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
</resources>