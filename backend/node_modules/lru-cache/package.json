{"name": "lru-cache", "description": "A cache object that deletes the least-recently-used items.", "version": "6.0.0", "author": "<PERSON> <<EMAIL>>", "keywords": ["mru", "lru", "cache"], "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "main": "index.js", "repository": "git://github.com/isaacs/node-lru-cache.git", "devDependencies": {"benchmark": "^2.1.4", "tap": "^14.10.7"}, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "files": ["index.js"], "engines": {"node": ">=10"}}