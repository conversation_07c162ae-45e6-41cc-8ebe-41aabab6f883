package com.example.coffeeshop.activities;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.Order;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationBarView;

import org.json.JSONArray;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class OrderTrackingActivity extends AppCompatActivity {

    private TextView tvOrderId, tvOrderStatus, tvOrderTime, tvDeliveryAddress, tvOrderItems, tvOrderTotal, tvSpecialInstructions;
    private TextView tvStatusPending, tvStatusPreparing, tvStatusOnWay, tvStatusDelivered;
    private ProgressBar progressBar;
    private Button btnCancelOrder, btnBackToDashboard;
    private BottomNavigationView bottomNavigation;
    
    private MockDataManager dataManager;
    private ApiService apiService;
    private Order currentOrder;
    private int orderId;
    private int userId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_order_tracking);
        
        // Get order ID and user ID from intent
        orderId = getIntent().getIntExtra("orderId", -1);
        userId = getIntent().getIntExtra("userId", 1);
        
        dataManager = MockDataManager.getInstance();
        apiService = new ApiService();
        initViews();
        loadOrderDetails();
        setupListeners();
        setupBottomNavigation();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadOrderDetails(); // Refresh order status
    }

    private void initViews() {
        tvOrderId = findViewById(R.id.tv_order_id);
        tvOrderStatus = findViewById(R.id.tv_order_status);
        tvOrderTime = findViewById(R.id.tv_order_time);
        tvDeliveryAddress = findViewById(R.id.tv_delivery_address);
        
        // These may be null if layout doesn't have them - handle gracefully
        tvOrderItems = findViewById(R.id.tv_order_items);
        tvOrderTotal = findViewById(R.id.tv_order_total);
        tvSpecialInstructions = findViewById(R.id.tv_special_instructions);
        
        tvStatusPending = findViewById(R.id.tv_status_pending);
        tvStatusPreparing = findViewById(R.id.tv_status_preparing);
        tvStatusOnWay = findViewById(R.id.tv_status_on_way);
        tvStatusDelivered = findViewById(R.id.tv_status_delivered);
        
        progressBar = findViewById(R.id.progress_bar);
        btnCancelOrder = findViewById(R.id.btn_cancel_order);
        btnBackToDashboard = findViewById(R.id.btn_back_to_dashboard);
        bottomNavigation = findViewById(R.id.bottom_navigation);
    }

    private void loadOrderDetails() {
        // Load order from API first, then fallback to MockDataManager
        apiService.getOrder(orderId, new ApiService.OrderCallback() {
            @Override
            public void onSuccess(JSONObject orderJson) {
                try {
                    // Parse date from API format
                    Date orderDate;
                    try {
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                        orderDate = dateFormat.parse(orderJson.getString("created_at"));
                    } catch (Exception e) {
                        orderDate = new Date(); // Fallback to current date
                    }
                    
                    currentOrder = new Order(
                        orderJson.getInt("order_id"),
                        orderJson.getInt("customer_id"),
                        orderJson.getString("customer_name"),
                        orderJson.getString("status"),
                        new BigDecimal(orderJson.getDouble("total_price")),
                        null, // discount
                        orderDate
                    );
                    
                    // Set additional fields from API response
                    if (orderJson.has("items_summary")) {
                        currentOrder.setItemsSummary(orderJson.getString("items_summary"));
                    }
                    
                    updateOrderInfo(orderJson);
                    updateProgressBar();
                    
                } catch (Exception e) {
                    e.printStackTrace();
                    loadOrderDetailsFromMock();
                }
            }

            @Override
            public void onError(String error) {
                loadOrderDetailsFromMock();
            }
        });
    }
    
    private void loadOrderDetailsFromMock() {
        currentOrder = dataManager.findOrderById(orderId);
        
        if (currentOrder == null) {
            Toast.makeText(this, "Order not found", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        updateOrderInfo(null);
        updateProgressBar();
    }

    private void updateOrderInfo(JSONObject orderJson) {
        tvOrderId.setText("Order #" + currentOrder.getOrderId());
        tvOrderStatus.setText("Status: " + currentOrder.getStatus().toUpperCase());
        tvOrderTime.setText("Ordered: " + currentOrder.getFormattedCreatedAt());
        
        if (tvOrderTotal != null) {
            tvOrderTotal.setText("Total: " + currentOrder.getFormattedTotalPrice());
        }
        
        // Display delivery address from API or fallback to mock
        String deliveryAddress = "123 Main Street"; // Default fallback
        if (orderJson != null && orderJson.has("delivery_address")) {
            try {
                deliveryAddress = orderJson.getString("delivery_address");
            } catch (Exception e) {
                // Use fallback
            }
        }
        tvDeliveryAddress.setText("Delivery to: " + deliveryAddress);
        
        // Display order items from API
        StringBuilder itemsText = new StringBuilder();
        if (orderJson != null && orderJson.has("items")) {
            try {
                JSONArray items = orderJson.getJSONArray("items");
                for (int i = 0; i < items.length(); i++) {
                    JSONObject item = items.getJSONObject(i);
                    if (i > 0) itemsText.append("\n");
                    itemsText.append("• ")
                            .append(item.getString("product_name"))
                            .append(" x")
                            .append(item.getInt("quantity"))
                            .append(" - $")
                            .append(String.format("%.2f", item.getDouble("unit_price") * item.getInt("quantity")));
                }
            } catch (Exception e) {
                // Fallback to items summary if items array fails
                if (currentOrder.getItemsSummary() != null) {
                    itemsText.append(currentOrder.getItemsSummary());
                } else {
                    itemsText.append("Order items not available");
                }
            }
        } else if (currentOrder.getItemsSummary() != null) {
            itemsText.append(currentOrder.getItemsSummary());
        } else {
            itemsText.append("Order items not available");
        }
        
        if (tvOrderItems != null) {
            tvOrderItems.setText(itemsText.toString());
        }
        
        // Display special instructions from API
        String specialInstructions = "No special instructions";
        if (orderJson != null && orderJson.has("special_instructions")) {
            try {
                String instructions = orderJson.getString("special_instructions");
                if (instructions != null && !instructions.trim().isEmpty()) {
                    specialInstructions = instructions;
                }
            } catch (Exception e) {
                // Use default
            }
        }
        if (tvSpecialInstructions != null) {
            tvSpecialInstructions.setText("Special Instructions: " + specialInstructions);
        }
    }

    private void updateProgressBar() {
        String status = currentOrder.getStatus().toLowerCase();
        
        // Reset all status indicators
        resetStatusIndicators();
        
        switch (status) {
            case "pending":
                progressBar.setProgress(25);
                tvStatusPending.setTextColor(getResources().getColor(R.color.primary));
                tvStatusPending.setBackgroundResource(R.drawable.status_active);
                break;
            case "preparing":
                progressBar.setProgress(50);
                tvStatusPending.setTextColor(getResources().getColor(R.color.accent));
                tvStatusPending.setBackgroundResource(R.drawable.status_completed);
                tvStatusPreparing.setTextColor(getResources().getColor(R.color.primary));
                tvStatusPreparing.setBackgroundResource(R.drawable.status_active);
                break;
            case "on_way":
            case "on the way":
                progressBar.setProgress(75);
                tvStatusPending.setTextColor(getResources().getColor(R.color.accent));
                tvStatusPending.setBackgroundResource(R.drawable.status_completed);
                tvStatusPreparing.setTextColor(getResources().getColor(R.color.accent));
                tvStatusPreparing.setBackgroundResource(R.drawable.status_completed);
                tvStatusOnWay.setTextColor(getResources().getColor(R.color.primary));
                tvStatusOnWay.setBackgroundResource(R.drawable.status_active);
                break;
            case "delivered":
            case "finished":
                progressBar.setProgress(100);
                tvStatusPending.setTextColor(getResources().getColor(R.color.accent));
                tvStatusPending.setBackgroundResource(R.drawable.status_completed);
                tvStatusPreparing.setTextColor(getResources().getColor(R.color.accent));
                tvStatusPreparing.setBackgroundResource(R.drawable.status_completed);
                tvStatusOnWay.setTextColor(getResources().getColor(R.color.accent));
                tvStatusOnWay.setBackgroundResource(R.drawable.status_completed);
                tvStatusDelivered.setTextColor(getResources().getColor(R.color.accent));
                tvStatusDelivered.setBackgroundResource(R.drawable.status_completed);
                btnCancelOrder.setEnabled(false);
                btnCancelOrder.setText("Order Completed");
                break;
            case "cancelled":
                progressBar.setProgress(0);
                tvOrderStatus.setText("Status: CANCELLED");
                tvOrderStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                btnCancelOrder.setEnabled(false);
                btnCancelOrder.setText("Order Cancelled");
                break;
        }
        
        // Hide cancel button if order is finished or delivered or already cancelled
        if ("finished".equals(status) || "delivered".equals(status) || "cancelled".equals(status) || "ready".equals(status)) {
            btnCancelOrder.setEnabled(false);
        } else {
            btnCancelOrder.setEnabled(true);
            btnCancelOrder.setText("Cancel Order");
        }
    }

    private void resetStatusIndicators() {
        int defaultColor = getResources().getColor(android.R.color.darker_gray);
        int defaultBackground = R.drawable.status_pending;
        
        tvStatusPending.setTextColor(defaultColor);
        tvStatusPending.setBackgroundResource(defaultBackground);
        tvStatusPreparing.setTextColor(defaultColor);
        tvStatusPreparing.setBackgroundResource(defaultBackground);
        tvStatusOnWay.setTextColor(defaultColor);
        tvStatusOnWay.setBackgroundResource(defaultBackground);
        tvStatusDelivered.setTextColor(defaultColor);
        tvStatusDelivered.setBackgroundResource(defaultBackground);
    }

    private void setupListeners() {
        btnCancelOrder.setOnClickListener(v -> showCancelOrderDialog());
        btnBackToDashboard.setOnClickListener(v -> {
            Intent intent = new Intent(this, CustomerDashboardActivity.class);
            intent.putExtra("userId", userId);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
            finish();
        });
        
        findViewById(R.id.btn_back).setOnClickListener(v -> finish());
    }

    private void showCancelOrderDialog() {
        // Check if order can be cancelled (typically only pending orders)
        if (currentOrder != null && !"pending".equals(currentOrder.getStatus().toLowerCase())) {
            Toast.makeText(this, "This order can no longer be cancelled", Toast.LENGTH_SHORT).show();
            return;
        }
        
        new AlertDialog.Builder(this)
                .setTitle("Cancel Order")
                .setMessage("Are you sure you want to cancel this order? This action cannot be undone.")
                .setPositiveButton("Yes, Cancel", (dialog, which) -> cancelOrder())
                .setNegativeButton("No", null)
                .show();
    }

    private void cancelOrder() {
        // First try to cancel via API, then fallback to MockDataManager
        apiService.updateOrderStatus(orderId, "cancelled", new ApiService.OrderActionCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                Toast.makeText(OrderTrackingActivity.this, "Order cancelled successfully", Toast.LENGTH_SHORT).show();
                loadOrderDetails(); // Refresh to show cancelled status
            }

            @Override
            public void onError(String error) {
                // Fallback to MockDataManager for offline functionality
                boolean success = dataManager.updateOrderStatus(orderId, "cancelled");
                
                if (success) {
                    Toast.makeText(OrderTrackingActivity.this, "Order cancelled successfully (offline)", Toast.LENGTH_SHORT).show();
                    loadOrderDetails(); // Refresh to show cancelled status
                } else {
                    Toast.makeText(OrderTrackingActivity.this, "Failed to cancel order: " + error, Toast.LENGTH_SHORT).show();
                }
            }
        });
    }
    
    private void setupBottomNavigation() {
        if (bottomNavigation == null) return;

        bottomNavigation.setSelectedItemId(R.id.nav_orders);
        bottomNavigation.setOnItemSelectedListener(new NavigationBarView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_home) {
                    // Navigate to home
                    Intent intent = new Intent(OrderTrackingActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", userId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_menu) {
                    // Navigate to menu (home)
                    Intent intent = new Intent(OrderTrackingActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", userId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    // Already on orders screen
                    return true;
                } else if (itemId == R.id.nav_favorites) {
                    // Show a simple message since favorites isn't implemented
                    Toast.makeText(OrderTrackingActivity.this, "Favorites - Coming Soon!", Toast.LENGTH_SHORT).show();
                    return true;
                } else if (itemId == R.id.nav_profile) {
                    // Navigate to Profile
                    Intent intent = new Intent(OrderTrackingActivity.this, ProfileActivity.class);
                    intent.putExtra("userId", userId);
                    startActivity(intent);
                    finish();
                    return true;
                }
                return false;
            }
        });
    }
}
