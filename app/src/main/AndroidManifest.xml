<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.CoffeeShop"
        tools:targetApi="31">

        <!-- Login Activity - Main Entry Point -->
        <activity
            android:name=".activities.LoginActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>        <!-- Register Activity -->
        <activity
            android:name=".activities.RegisterActivity"
            android:exported="false" />

        <!-- Cart Activity -->
        <activity
            android:name=".activities.CartActivity"
            android:exported="false" />

        <!-- Role-specific Dashboard Activities -->
        <activity
            android:name=".activities.CustomerDashboardActivity"
            android:exported="false" />

        <activity
            android:name=".activities.BaristaDashboardActivity"
            android:exported="false" />

        <activity
            android:name=".activities.ShipperDashboardActivity"
            android:exported="false" />

        <activity
            android:name=".activities.ManagerDashboardActivity"
            android:exported="false" />

        <activity
            android:name=".activities.CustomerSupportDashboardActivity"
            android:exported="false" />

        <!-- Product Details Activity -->
        <activity
            android:name=".activities.ProductDetailsActivity"
            android:exported="false" />

        <!-- Customer Screens -->
        <activity
            android:name=".activities.CheckoutActivity"
            android:exported="false" />

        <activity
            android:name=".activities.OrderTrackingActivity"
            android:exported="false" />

        <activity
            android:name=".activities.OrderHistoryActivity"
            android:exported="false" />

        <activity
            android:name=".activities.ProfileActivity"
            android:exported="false" />

        <!-- Barista Screens -->
        <activity
            android:name=".activities.BaristaOrderQueueActivity"
            android:exported="false" />

        <activity
            android:name=".activities.BaristaRecipeGuideActivity"
            android:exported="false" />

        <activity
            android:name=".activities.RecipeDetailActivity"
            android:exported="false" />

        <activity
            android:name=".activities.BaristaInventoryActivity"
            android:exported="false" />

        <activity
            android:name=".activities.BaristaReadyOrdersActivity"
            android:exported="false" />

        <activity
            android:name=".activities.BaristaDailyTasksActivity"
            android:exported="false" />

        <activity
            android:name=".activities.BaristaQuickActionsActivity"
            android:exported="false" />
    </application>

</manifest>