package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.util.Patterns;
import android.widget.ArrayAdapter;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.radiobutton.MaterialRadioButton;
import com.google.android.material.textfield.MaterialAutoCompleteTextView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.example.coffeeshop.R;

public class EditAccountActivity extends AppCompatActivity {

    private Toolbar toolbar;
    private TextInputEditText etUsername, etEmail, etPhone;
    private TextInputLayout tilUsername, tilEmail, tilPhone, tilRole;
    private MaterialAutoCompleteTextView spinnerRole;
    private RadioGroup rgStatus;
    private MaterialRadioButton rbActive, rbInactive;
    private MaterialButton btnEdit, btnCancel, btnDelete, btnResetPassword;
    private TextView tvLastLogin;

    private String originalUsername, originalEmail, originalPhone, originalRole, originalStatus, originalDateCreated;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            setContentView(R.layout.activity_edit_account);

            initViews();
            setupToolbar();
            setupDropdowns();
            loadAccountData();
            setupClickListeners();

        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Error loading edit screen: " + e.getMessage(), Toast.LENGTH_LONG).show();
            finish();
        }
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        etUsername = findViewById(R.id.et_username);
        etEmail = findViewById(R.id.et_email);
        etPhone = findViewById(R.id.et_phone);
        tilUsername = findViewById(R.id.til_username);
        tilEmail = findViewById(R.id.til_email);
        tilPhone = findViewById(R.id.til_phone);
        tilRole = findViewById(R.id.til_role);
        spinnerRole = findViewById(R.id.spinner_role);
        rgStatus = findViewById(R.id.rg_status);
        rbActive = findViewById(R.id.rb_active);
        rbInactive = findViewById(R.id.rb_inactive);
        btnEdit = findViewById(R.id.btn_edit);
        btnCancel = findViewById(R.id.btn_cancel);
        btnDelete = findViewById(R.id.btn_delete);
        btnResetPassword = findViewById(R.id.btn_reset_password);
        tvLastLogin = findViewById(R.id.tv_last_login);

        // Check if any views are null
        if (toolbar == null || etUsername == null || spinnerRole == null) {
            throw new RuntimeException("Required views not found in layout");
        }
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Edit Account");
        }
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
    }

    private void setupDropdowns() {
        String[] roles = {"Barista", "Manager", "Shipper", "Support"};
        ArrayAdapter<String> roleAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, roles);
        spinnerRole.setAdapter(roleAdapter);
    }

    private void loadAccountData() {
        originalUsername = getIntent().getStringExtra("username");
        originalEmail = getIntent().getStringExtra("email");
        originalPhone = getIntent().getStringExtra("phone");
        originalRole = getIntent().getStringExtra("role");
        originalStatus = getIntent().getStringExtra("status");
        originalDateCreated = getIntent().getStringExtra("dateCreated");

        // Populate fields with null checks
        etUsername.setText(originalUsername != null ? originalUsername : "");
        etEmail.setText(originalEmail != null ? originalEmail : "");
        etPhone.setText(originalPhone != null ? originalPhone : "");

        if (originalRole != null) {
            spinnerRole.setText(originalRole, false);
        }

        // Set status radio button
        if ("Active".equals(originalStatus)) {
            rbActive.setChecked(true);
        } else if ("Inactive".equals(originalStatus)) {
            rbInactive.setChecked(true);
        } else {
            rbActive.setChecked(true); // Default to active
        }

        if (tvLastLogin != null) {
            tvLastLogin.setText("Today, 2:30 PM");
        }
    }

    private void setupClickListeners() {
        btnEdit.setOnClickListener(v -> saveAccount());
        btnCancel.setOnClickListener(v -> onBackPressed());
        btnDelete.setOnClickListener(v -> showDeleteConfirmation());
        btnResetPassword.setOnClickListener(v -> resetPassword());
    }

    private void saveAccount() {
        if (!validateInputs()) {
            return;
        }

        String username = etUsername.getText().toString().trim();
        String email = etEmail.getText().toString().trim();
        String phone = etPhone.getText().toString().trim();
        String role = spinnerRole.getText().toString().trim();
        String status = rbActive.isChecked() ? "Active" : "Inactive";

        boolean hasChanges = !username.equals(originalUsername != null ? originalUsername : "") ||
                !email.equals(originalEmail != null ? originalEmail : "") ||
                !phone.equals(originalPhone != null ? originalPhone : "") ||
                !role.equals(originalRole != null ? originalRole : "") ||
                !status.equals(originalStatus != null ? originalStatus : "");

        if (!hasChanges) {
            Toast.makeText(this, "No changes detected", Toast.LENGTH_SHORT).show();
            return;
        }

        btnEdit.setEnabled(false);
        btnEdit.setText("Saving...");

        new android.os.Handler().postDelayed(() -> {
            btnEdit.setEnabled(true);
            btnEdit.setText("Save Changes");
            Toast.makeText(this, "Account updated successfully", Toast.LENGTH_SHORT).show();
            finish();
        }, 1500);
    }

    private boolean validateInputs() {
        boolean isValid = true;

        tilUsername.setError(null);
        tilEmail.setError(null);
        tilPhone.setError(null);
        tilRole.setError(null);

        String username = etUsername.getText().toString().trim();
        if (username.isEmpty()) {
            tilUsername.setError("Username is required");
            isValid = false;
        } else if (username.length() < 3) {
            tilUsername.setError("Username must be at least 3 characters");
            isValid = false;
        }

        String email = etEmail.getText().toString().trim();
        if (email.isEmpty()) {
            tilEmail.setError("Email is required");
            isValid = false;
        } else if (!Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            tilEmail.setError("Please enter a valid email address");
            isValid = false;
        }

        String phone = etPhone.getText().toString().trim();
        if (phone.isEmpty()) {
            tilPhone.setError("Phone number is required");
            isValid = false;
        } else if (phone.length() < 10) {
            tilPhone.setError("Please enter a valid phone number");
            isValid = false;
        }

        String role = spinnerRole.getText().toString().trim();
        if (role.isEmpty()) {
            tilRole.setError("Please select a role");
            isValid = false;
        }

        if (!rbActive.isChecked() && !rbInactive.isChecked()) {
            Toast.makeText(this, "Please select account status", Toast.LENGTH_SHORT).show();
            isValid = false;
        }

        return isValid;
    }

    private void showDeleteConfirmation() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Delete Account")
                .setMessage("Are you sure you want to delete this account? This action cannot be undone.")
                .setIcon(android.R.drawable.ic_dialog_alert)
                .setPositiveButton("Delete", (dialog, which) -> deleteAccount())
                .setNegativeButton("Cancel", null)
                .show();
    }

    private void deleteAccount() {
        btnDelete.setEnabled(false);
        btnDelete.setText("Deleting...");

        new android.os.Handler().postDelayed(() -> {
            btnDelete.setEnabled(true);
            btnDelete.setText("Delete Account");
            Toast.makeText(this, "Account deleted successfully", Toast.LENGTH_SHORT).show();
            finish();
        }, 1500);
    }

    private void resetPassword() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Reset Password")
                .setMessage("A password reset link will be sent to " + (originalEmail != null ? originalEmail : "the user's email") + ". Continue?")
                .setIcon(android.R.drawable.ic_dialog_info)
                .setPositiveButton("Send Reset Link", (dialog, which) -> {
                    Toast.makeText(this, "Password reset link sent successfully", Toast.LENGTH_LONG).show();
                })
                .setNegativeButton("Cancel", null)
                .show();
    }

    @Override
    public void onBackPressed() {
        String username = etUsername.getText().toString().trim();
        String email = etEmail.getText().toString().trim();
        String phone = etPhone.getText().toString().trim();
        String role = spinnerRole.getText().toString().trim();
        String status = rbActive.isChecked() ? "Active" : "Inactive";

        boolean hasChanges = !username.equals(originalUsername != null ? originalUsername : "") ||
                !email.equals(originalEmail != null ? originalEmail : "") ||
                !phone.equals(originalPhone != null ? originalPhone : "") ||
                !role.equals(originalRole != null ? originalRole : "") ||
                !status.equals(originalStatus != null ? originalStatus : "");

        if (hasChanges) {
            new androidx.appcompat.app.AlertDialog.Builder(this)
                    .setTitle("Unsaved Changes")
                    .setMessage("You have unsaved changes. Are you sure you want to leave?")
                    .setPositiveButton("Leave", (dialog, which) -> super.onBackPressed())
                    .setNegativeButton("Stay", null)
                    .show();
        } else {
            super.onBackPressed();
        }
    }
}