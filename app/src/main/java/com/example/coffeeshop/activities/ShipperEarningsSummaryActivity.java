package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;

public class ShipperEarningsSummaryActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_earnings_summary);

        TextView tvTotal = findViewById(R.id.tv_total_earnings);
        TextView tvBreakdown = findViewById(R.id.tv_earnings_breakdown);

        // Dữ liệu giả
        int delivery1 = 35000;
        int delivery2 = 42000;
        int delivery3 = 50000;

        int total = delivery1 + delivery2 + delivery3;

        String breakdown = "Delivery #ORD101: 35,000 VND\n"
                + "Delivery #ORD102: 42,000 VND\n"
                + "Delivery #ORD103: 50,000 VND";

        tvTotal.setText("Total Earnings: " + total + " VND");
        tvBreakdown.setText(breakdown);
    }
}
