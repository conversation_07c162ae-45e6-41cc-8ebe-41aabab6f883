package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.User;

import org.json.JSONObject;

public class RegisterActivity extends AppCompatActivity {

    private EditText etFullName, etEmail, etPhone, etPassword, etConfirmPassword;
    private Button btnRegister;
    private TextView tvLogin;
    private MockDataManager dataManager;
    private ApiService apiService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_register);

        initViews();
        dataManager = MockDataManager.getInstance();
        apiService = new ApiService();
        setupClickListeners();
    }

    private void initViews() {
        etFullName = findViewById(R.id.et_full_name);
        etEmail = findViewById(R.id.et_email);
        etPhone = findViewById(R.id.et_phone);
        etPassword = findViewById(R.id.et_password);
        etConfirmPassword = findViewById(R.id.et_confirm_password);
        btnRegister = findViewById(R.id.btn_register);
        tvLogin = findViewById(R.id.tv_login);
    }

    private void setupClickListeners() {
        btnRegister.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                performRegistration();
            }
        });

        tvLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish(); // Go back to login
            }
        });
    }

    private void performRegistration() {
        String fullName = etFullName.getText().toString().trim();
        String email = etEmail.getText().toString().trim();
        String phone = etPhone.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        String confirmPassword = etConfirmPassword.getText().toString().trim();

        // Validation
        if (fullName.isEmpty()) {
            etFullName.setError("Full name is required");
            etFullName.requestFocus();
            return;
        }

        if (email.isEmpty()) {
            etEmail.setError("Email is required");
            etEmail.requestFocus();
            return;
        }

        if (!isValidEmail(email)) {
            etEmail.setError("Please enter a valid email");
            etEmail.requestFocus();
            return;
        }

        if (password.isEmpty()) {
            etPassword.setError("Password is required");
            etPassword.requestFocus();
            return;
        }

        if (password.length() < 6) {
            etPassword.setError("Password must be at least 6 characters");
            etPassword.requestFocus();
            return;
        }

        if (!password.equals(confirmPassword)) {
            etConfirmPassword.setError("Passwords do not match");
            etConfirmPassword.requestFocus();
            return;
        }

        // Attempt registration
        // Disable register button to prevent multiple requests
        btnRegister.setEnabled(false);
        btnRegister.setText("Registering...");

        // Try API first, then fallback to MockDataManager
        apiService.register(fullName, email, phone, password, new ApiService.AuthCallback() {
            @Override
            public void onSuccess(JSONObject userJson) {
                Toast.makeText(RegisterActivity.this, "Registration successful! You can now login.", Toast.LENGTH_LONG).show();
                btnRegister.setEnabled(true);
                btnRegister.setText("Register");
                finish(); // Go back to login
            }

            @Override
            public void onError(String error) {
                // Fallback to MockDataManager
                User newUser = dataManager.registerUser(fullName, email, phone, password);
                if (newUser != null) {
                    Toast.makeText(RegisterActivity.this, "Registration successful! You can now login. (Offline)", Toast.LENGTH_LONG).show();
                    finish(); // Go back to login
                } else {
                    Toast.makeText(RegisterActivity.this, "Email already exists. Please use a different email.", Toast.LENGTH_SHORT).show();
                }
                
                btnRegister.setEnabled(true);
                btnRegister.setText("Register");
            }
        });
    }

    private boolean isValidEmail(String email) {
        return email.contains("@") && email.contains(".");
    }
}
