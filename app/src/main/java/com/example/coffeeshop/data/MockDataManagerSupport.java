package com.example.coffeeshop.data;

import com.example.coffeeshop.models.SupportTicket;
import com.example.coffeeshop.models.SupportResponse;
import com.example.coffeeshop.models.PaymentFailure;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MockDataManagerSupport {
    private static MockDataManagerSupport instance;
    private List<SupportTicket> tickets;
    private List<PaymentFailure> paymentFailures;

    private MockDataManagerSupport() {
        tickets = new ArrayList<>();
        paymentFailures = new ArrayList<>();
        // Add mock tickets
        tickets.add(new SupportTicket(1, 1, "<PERSON>", "Late Delivery", "My order was late.", "Open", "High", new ArrayList<>(Arrays.asList(
            new SupportResponse(1, 1, 1, "Customer", "Order was late.", "2025-07-01 10:00"),
            new SupportResponse(2, 1, 2, "Staff", "Sorry for the delay! We'll look into it.", "2025-07-01 10:05")
        )), 101, "2025-07-01 09:55", "2025-07-01 10:05"));
        tickets.add(new SupportTicket(2, 2, "<PERSON> Tran", "Wrong Item", "Received wrong drink.", "In Progress", "Medium", new ArrayList<>(Arrays.asList(
            new SupportResponse(3, 2, 2, "Staff", "We are checking your order.", "2025-07-01 11:00")
        )), 102, "2025-07-01 10:50", "2025-07-01 11:00"));
        tickets.add(new SupportTicket(3, 3, "Carol Le", "Missing Item", "Missing a pastry.", "Resolved", "Low", new ArrayList<>(Arrays.asList(
            new SupportResponse(4, 3, 3, "Customer", "My croissant is missing.", "2025-07-01 09:30"),
            new SupportResponse(5, 3, 2, "Staff", "We will refund you for the missing item.", "2025-07-01 09:35")
        )), 103, "2025-07-01 09:25", "2025-07-01 09:35"));
        tickets.add(new SupportTicket(4, 4, "David Pham", "Payment Issue", "Payment failed but money deducted.", "Escalated", "Urgent", new ArrayList<>(Arrays.asList(
            new SupportResponse(6, 4, 4, "Customer", "Payment failed but money deducted.", "2025-07-01 08:00")
        )), 104, "2025-07-01 07:55", "2025-07-01 08:00"));
        // Add mock payment failure
        paymentFailures.add(new PaymentFailure(1, 101, 1, "Card declined", "Pending", "2025-07-01 09:50"));
    }

    public static MockDataManagerSupport getInstance() {
        if (instance == null) instance = new MockDataManagerSupport();
        return instance;
    }

    public List<SupportTicket> getTicketsForCustomer(int customerId) {
        List<SupportTicket> result = new ArrayList<>();
        for (SupportTicket t : tickets) if (t.customerId == customerId) result.add(t);
        return result;
    }

    public SupportTicket getTicketById(int ticketId) {
        for (SupportTicket t : tickets) if (t.id == ticketId) return t;
        // If not found, create a mock ticket for testing to avoid null pointer exception
        if (ticketId > 0) {
            SupportTicket mock = new SupportTicket(ticketId, 999, "Mock User", "Mock Subject", "Mock Description", "Open", "Low", new ArrayList<>(), 9999, "2025-07-01 00:00", "2025-07-01 00:00");
            tickets.add(mock);
            System.out.println("[MockDataManagerSupport] Warning: Ticket ID " + ticketId + " not found. Returning mock ticket.");
            return mock;
        }
        return null;
    }

    public List<PaymentFailure> getPaymentFailuresForCustomer(int customerId) {
        List<PaymentFailure> result = new ArrayList<>();
        for (PaymentFailure pf : paymentFailures) if (pf.customerId == customerId) result.add(pf);
        return result;
    }

    public void addResponseToTicket(int ticketId, SupportResponse response) {
        SupportTicket t = getTicketById(ticketId);
        if (t != null) t.responses.add(response);
    }

    public void updateTicketStatus(int ticketId, String status) {
        SupportTicket t = getTicketById(ticketId);
        if (t != null) t.status = status;
    }

    public void escalateTicket(int ticketId) {
        updateTicketStatus(ticketId, "Escalated");
    }

    public void processRefund(int orderId) {
        // Mock: just print or set a flag
    }

    public void resolvePaymentFailure(int paymentFailureId) {
        for (PaymentFailure pf : paymentFailures) if (pf.id == paymentFailureId) pf.status = "Resolved";
    }

    public void assignTicketToStaff(int ticketId, String staffName) {
        SupportTicket t = getTicketById(ticketId);
        if (t != null) t.assignedStaffName = staffName;
    }
}
