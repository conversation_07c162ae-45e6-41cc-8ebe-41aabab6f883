<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Filter Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:elevation="4dp">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <EditText
                    android:id="@+id/editFilterId"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="Ticket ID"/>
                <EditText
                    android:id="@+id/editFilterName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="Customer Name"
                    android:layout_marginStart="8dp"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">
                <Spinner
                    android:id="@+id/spinnerStatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:prompt="@string/status_prompt"/>
                <Spinner
                    android:id="@+id/spinnerPriority"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:prompt="@string/priority_prompt"
                    android:layout_marginStart="8dp"/>
                <Button
                    android:id="@+id/btnExport"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Export"
                    android:layout_marginStart="8dp"/>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Ticket List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewSupportTickets"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>
</LinearLayout>
