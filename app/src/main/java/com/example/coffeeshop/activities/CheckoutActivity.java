package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.CheckoutItemAdapter;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.CartItem;
import com.example.coffeeshop.models.Order;
import com.example.coffeeshop.models.OrderItem;
import com.example.coffeeshop.models.Product;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationBarView;
import com.google.android.material.textfield.TextInputEditText;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CheckoutActivity extends AppCompatActivity {

    private RecyclerView rvCheckoutItems;
    private TextView tvSubtotal, tvDeliveryFee, tvTotal;
    private TextInputEditText etDeliveryAddress, etSpecialInstructions;
    private RadioGroup rgPaymentMethod;
    private Button btnPlaceOrder;
    private BottomNavigationView bottomNavigation;
    
    private MockDataManager dataManager;
    private CheckoutItemAdapter adapter;
    private List<CartItem> cartItems;
    private BigDecimal subtotal = BigDecimal.ZERO;
    private BigDecimal deliveryFee = new BigDecimal("2.50");
    private int currentUserId = 1; // Default customer

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_checkout);
        
        // Get user ID from intent
        currentUserId = getIntent().getIntExtra("userId", 1);
        
        dataManager = MockDataManager.getInstance();
        initViews();
        setupBottomNavigation();
        loadCartItems();
        setupRecyclerView();
        calculateTotals();
        setupListeners();
    }

    private void initViews() {
        rvCheckoutItems = findViewById(R.id.rv_checkout_items);
        tvSubtotal = findViewById(R.id.tv_subtotal);
        tvDeliveryFee = findViewById(R.id.tv_delivery_fee);
        tvTotal = findViewById(R.id.tv_total);
        etDeliveryAddress = findViewById(R.id.et_delivery_address);
        etSpecialInstructions = findViewById(R.id.et_special_instructions);
        rgPaymentMethod = findViewById(R.id.rg_payment_method);
        btnPlaceOrder = findViewById(R.id.btn_place_order);
        bottomNavigation = findViewById(R.id.bottom_navigation);
    }

    private void loadCartItems() {
        cartItems = dataManager.getCartItemsByUserId(currentUserId);
    }

    private void setupRecyclerView() {
        adapter = new CheckoutItemAdapter(cartItems);
        rvCheckoutItems.setLayoutManager(new LinearLayoutManager(this));
        rvCheckoutItems.setAdapter(adapter);
    }

    private void calculateTotals() {
        subtotal = BigDecimal.ZERO;
        
        for (CartItem item : cartItems) {
            BigDecimal itemTotal = item.getUnitPrice().multiply(new BigDecimal(item.getQuantity()));
            subtotal = subtotal.add(itemTotal);
        }
        
        BigDecimal total = subtotal.add(deliveryFee);
        
        tvSubtotal.setText("$" + subtotal.setScale(2, BigDecimal.ROUND_HALF_UP));
        tvDeliveryFee.setText("$" + deliveryFee.setScale(2, BigDecimal.ROUND_HALF_UP));
        tvTotal.setText("$" + total.setScale(2, BigDecimal.ROUND_HALF_UP));
    }

    private void setupListeners() {
        btnPlaceOrder.setOnClickListener(v -> placeOrder());
        
        findViewById(R.id.btn_back).setOnClickListener(v -> finish());
    }

    private void placeOrder() {
        // Validate inputs
        String address = etDeliveryAddress.getText().toString().trim();
        if (address.isEmpty()) {
            etDeliveryAddress.setError("Delivery address is required");
            etDeliveryAddress.requestFocus();
            return;
        }
        
        if (cartItems.isEmpty()) {
            Toast.makeText(this, "Your cart is empty", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Get selected payment method
        int selectedPaymentId = rgPaymentMethod.getCheckedRadioButtonId();
        String paymentMethod = "Cash on Delivery"; // Default
        if (selectedPaymentId == R.id.rb_qr_payment) {
            paymentMethod = "QR Payment";
        }
        
        try {
            // Create order
            BigDecimal totalAmount = subtotal.add(deliveryFee);
            String specialInstructions = etSpecialInstructions.getText().toString().trim();
            
            Order order = new Order(
                0, // Will be set by data manager
                currentUserId,
                "Customer", // Customer name - will be updated by data manager
                "pending",
                totalAmount,
                null, // No discount
                new Date()
            );
            
            // Initialize order items list
            order.setOrderItems(new ArrayList<>());
            
            // Add order items
            for (CartItem cartItem : cartItems) {
                Product product = dataManager.findProductById(cartItem.getProductId());
                String productName = (product != null) ? product.getName() : "Unknown Product";
                
                OrderItem orderItem = new OrderItem(
                    0, // Will be set by data manager
                    0, // Order ID will be set after order creation
                    cartItem.getProductId(),
                    productName,
                    cartItem.getQuantity(),
                    cartItem.getUnitPrice()
                );
                order.getOrderItems().add(orderItem);
            }
            
            // Save order and clear cart
            dataManager.addOrder(order);
            dataManager.clearCart(currentUserId);
            
            Toast.makeText(this, "Order placed successfully!", Toast.LENGTH_SHORT).show();
            
            // Navigate to order tracking
            Intent intent = new Intent(this, OrderTrackingActivity.class);
            intent.putExtra("orderId", order.getOrderId());
            intent.putExtra("userId", currentUserId);
            startActivity(intent);
            finish();
            
        } catch (Exception e) {
            Toast.makeText(this, "Error placing order: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    
    private void setupBottomNavigation() {
        if (bottomNavigation == null) return;

        bottomNavigation.setSelectedItemId(R.id.nav_menu); // Checkout is part of menu flow
        bottomNavigation.setOnItemSelectedListener(new NavigationBarView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_home) {
                    // Navigate to home
                    Intent intent = new Intent(CheckoutActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_menu) {
                    // Navigate to menu (home)
                    Intent intent = new Intent(CheckoutActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    // Navigate to Order History
                    Intent intent = new Intent(CheckoutActivity.this, OrderHistoryActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_favorites) {
                    // Show a simple message since favorites isn't implemented
                    Toast.makeText(CheckoutActivity.this, "Favorites - Coming Soon!", Toast.LENGTH_SHORT).show();
                    return true;
                } else if (itemId == R.id.nav_profile) {
                    // Navigate to Profile
                    Intent intent = new Intent(CheckoutActivity.this, ProfileActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                }
                return false;
            }
        });
    }
}
