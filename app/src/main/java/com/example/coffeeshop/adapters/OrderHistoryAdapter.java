package com.example.coffeeshop.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.models.Order;
import com.example.coffeeshop.models.OrderItem;

import java.util.List;

public class OrderHistoryAdapter extends RecyclerView.Adapter<OrderHistoryAdapter.ViewHolder> {

    private List<Order> orderHistory;
    private OnOrderHistoryActionListener listener;

    public interface OnOrderHistoryActionListener {
        void onViewOrderDetails(Order order);
        void onReorderItems(Order order);
    }

    public OrderHistoryAdapter(List<Order> orderHistory, OnOrderHistoryActionListener listener) {
        this.orderHistory = orderHistory;
        this.listener = listener;
    }

    public void updateOrderHistory(List<Order> newOrderHistory) {
        this.orderHistory = newOrderHistory;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_order_history, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Order order = orderHistory.get(position);

        holder.tvOrderId.setText("Order #" + order.getOrderId());
        holder.tvOrderDate.setText(order.getFormattedCreatedAt());
        holder.tvOrderStatus.setText(order.getStatus().toUpperCase());
        holder.tvOrderTotal.setText(order.getFormattedTotalPrice());

        // Set status color
        String status = order.getStatus().toLowerCase();
        if ("delivered".equals(status) || "finished".equals(status)) {
            holder.tvOrderStatus.setTextColor(holder.itemView.getContext().getResources().getColor(R.color.accent));
        } else if ("cancelled".equals(status)) {
            holder.tvOrderStatus.setTextColor(holder.itemView.getContext().getResources().getColor(android.R.color.holo_red_dark));
        } else {
            holder.tvOrderStatus.setTextColor(holder.itemView.getContext().getResources().getColor(R.color.primary));
        }

        // Show order items summary
        StringBuilder itemsSummary = new StringBuilder();
        if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
            int itemCount = 0;
            for (OrderItem item : order.getOrderItems()) {
                itemCount += item.getQuantity();
            }
            itemsSummary.append(itemCount).append(" item");
            if (itemCount != 1) {
                itemsSummary.append("s");
            }

            // Show first few item names
            if (order.getOrderItems().size() <= 2) {
                itemsSummary.append(" • ");
                for (int i = 0; i < order.getOrderItems().size(); i++) {
                    if (i > 0) itemsSummary.append(", ");
                    itemsSummary.append(order.getOrderItems().get(i).getItemName());
                }
            } else {
                itemsSummary.append(" • ")
                        .append(order.getOrderItems().get(0).getItemName())
                        .append(" and ").append(order.getOrderItems().size() - 1).append(" more");
            }
        } else {
            // Check if we have items summary from API
            if (order.getItemsSummary() != null && !order.getItemsSummary().isEmpty()) {
                itemsSummary.append(order.getItemsSummary());
            } else {
                itemsSummary.append("No items");
            }
        }

        holder.tvOrderItems.setText(itemsSummary.toString());

        // Set up click listeners
        holder.btnViewDetails.setOnClickListener(v -> {
            if (listener != null) {
                listener.onViewOrderDetails(order);
            }
        });

        holder.btnReorder.setOnClickListener(v -> {
            if (listener != null) {
                listener.onReorderItems(order);
            }
        });

        // Enable reorder only for completed orders
        boolean canReorder = "delivered".equals(status) || "finished".equals(status);
        holder.btnReorder.setEnabled(canReorder);
        holder.btnReorder.setAlpha(canReorder ? 1.0f : 0.5f);
    }

    @Override
    public int getItemCount() {
        return orderHistory.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvOrderId, tvOrderDate, tvOrderStatus, tvOrderTotal, tvOrderItems;
        Button btnViewDetails, btnReorder;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvOrderId = itemView.findViewById(R.id.tv_order_id);
            tvOrderDate = itemView.findViewById(R.id.tv_order_date);
            tvOrderStatus = itemView.findViewById(R.id.tv_order_status);
            tvOrderTotal = itemView.findViewById(R.id.tv_order_total);
            tvOrderItems = itemView.findViewById(R.id.tv_order_items);
            btnViewDetails = itemView.findViewById(R.id.btn_view_details);
            btnReorder = itemView.findViewById(R.id.btn_reorder);
        }
    }
}
