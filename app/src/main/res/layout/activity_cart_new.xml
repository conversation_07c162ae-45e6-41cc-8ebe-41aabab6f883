<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="4dp"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:title="Your Cart"
            app:titleTextColor="@color/white" />
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nested_scroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <!-- Empty Cart Message -->
            <TextView
                android:id="@+id/tv_empty_cart"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="50dp"
                android:text="Your cart is empty\n\nAdd some delicious items to get started!"
                android:textAlignment="center"
                android:textAppearance="?attr/textAppearanceBody1"
                android:textColor="@color/text_secondary"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Cart Items RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_cart_items"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Cart Summary -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_summary"
                style="@style/Widget.CoffeeShop.Card"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rv_cart_items">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Order Summary"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Subtotal"
                            android:textAppearance="?attr/textAppearanceBody1" />

                        <TextView
                            android:id="@+id/tv_subtotal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="$0.00"
                            android:textAppearance="?attr/textAppearanceBody1" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:background="?android:attr/listDivider" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Total"
                            android:textAppearance="?attr/textAppearanceSubtitle1"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_total"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="$0.00"
                            android:textAppearance="?attr/textAppearanceSubtitle1"
                            android:textColor="@color/primary"
                            android:textStyle="bold" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="24dp"
                android:orientation="horizontal"
                android:gravity="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/card_summary">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_clear_cart"
                    style="@style/Widget.CoffeeShop.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="Clear Cart"
                    android:textColor="@color/error" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_checkout"
                    style="@style/Widget.CoffeeShop.Button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Checkout"
                    android:padding="16dp" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
