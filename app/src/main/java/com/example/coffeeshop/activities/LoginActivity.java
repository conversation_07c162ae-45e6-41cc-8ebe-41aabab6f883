package com.example.coffeeshop.activities;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.Role;
import com.example.coffeeshop.models.User;
import com.example.coffeeshop.utils.TestUsers;

import org.json.JSONObject;

public class LoginActivity extends AppCompatActivity {

    private EditText etEmail, etPassword;
    private Button btnLogin;
    private TextView tvRegister, tvTestUsers;
    private MockDataManager dataManager;
    private ApiService apiService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        initViews();
        dataManager = MockDataManager.getInstance();
        apiService = new ApiService();
        setupClickListeners();
    }

    private void initViews() {
        etEmail = findViewById(R.id.et_email);
        etPassword = findViewById(R.id.et_password);
        btnLogin = findViewById(R.id.btn_login);
        tvRegister = findViewById(R.id.tv_register);
        tvTestUsers = findViewById(R.id.tv_test_users);
    }

    private void setupClickListeners() {
        btnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                performLogin();
            }
        });

        tvRegister.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(LoginActivity.this, RegisterActivity.class);
                startActivity(intent);
            }
        });

        tvTestUsers.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTestUsersDialog();
            }
        });
    }

    private void showTestUsersDialog() {
        new AlertDialog.Builder(this)
                .setTitle("Test User Credentials")
                .setMessage(TestUsers.getTestCredentials())
                .setPositiveButton("OK", null)
                .show();
    }

    private void performLogin() {
        String email = etEmail.getText().toString().trim();
        String password = etPassword.getText().toString().trim();

        if (email.isEmpty()) {
            etEmail.setError("Email is required");
            etEmail.requestFocus();
            return;
        }

        if (password.isEmpty()) {
            etPassword.setError("Password is required");
            etPassword.requestFocus();
            return;
        }

        // Disable login button to prevent multiple requests
        btnLogin.setEnabled(false);
        btnLogin.setText("Logging in...");

        // Try API first, then fallback to MockDataManager
        apiService.login(email, password, new ApiService.AuthCallback() {
            @Override
            public void onSuccess(JSONObject userJson) {
                try {
                    // Save user data locally
                    saveUserData(userJson);
                    
                    String fullName = userJson.getString("full_name");
                    String role = userJson.getString("role");
                    
                    Toast.makeText(LoginActivity.this, "Login successful! Welcome " + fullName, Toast.LENGTH_SHORT).show();
                    navigateToRoleActivity(role);
                } catch (Exception e) {
                    e.printStackTrace();
                    Toast.makeText(LoginActivity.this, "Login data error", Toast.LENGTH_SHORT).show();
                } finally {
                    btnLogin.setEnabled(true);
                    btnLogin.setText("Login");
                }
            }

            @Override
            public void onError(String error) {
                // Fallback to MockDataManager
                User user = dataManager.loginUser(email, password);
                if (user != null) {
                    // Save user data locally (from mock data)
                    saveUserDataFromMock(user);
                    
                    Toast.makeText(LoginActivity.this, "Login successful! Welcome " + user.getFullName() + " (Offline)", Toast.LENGTH_SHORT).show();
                    navigateToRoleActivityFromMock(user.getRoleId());
                } else {
                    Toast.makeText(LoginActivity.this, "Invalid email or password", Toast.LENGTH_SHORT).show();
                }
                
                btnLogin.setEnabled(true);
                btnLogin.setText("Login");
            }
        });
    }

    private void saveUserData(JSONObject userJson) {
        try {
            SharedPreferences prefs = getSharedPreferences("user_data", MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putInt("user_id", userJson.getInt("user_id"));
            editor.putString("full_name", userJson.getString("full_name"));
            editor.putString("email", userJson.getString("email"));
            editor.putString("phone", userJson.optString("phone", ""));
            editor.putString("role", userJson.getString("role"));
            editor.putBoolean("logged_in", true);
            editor.apply();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void saveUserDataFromMock(User user) {
        SharedPreferences prefs = getSharedPreferences("user_data", MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt("user_id", user.getUserId());
        editor.putString("full_name", user.getFullName());
        editor.putString("email", user.getEmail());
        editor.putString("phone", user.getPhone());
        editor.putString("role", getRoleStringFromId(user.getRoleId()));
        editor.putBoolean("logged_in", true);
        editor.apply();
    }

    private String getRoleStringFromId(int roleId) {
        switch (roleId) {
            case Role.CUSTOMER: return "customer";
            case Role.BARISTA: return "barista";
            case Role.SHIPPER: return "shipper";
            case Role.MANAGER: return "manager";
            case Role.CUSTOMER_SUPPORT: return "support";
            default: return "customer";
        }
    }

    private void navigateToRoleActivity(String role) {
        Intent intent;
        switch (role.toLowerCase()) {
            case "customer":
                intent = new Intent(this, CustomerDashboardActivity.class);
                break;
            case "barista":
                intent = new Intent(this, BaristaDashboardActivity.class);
                break;
            case "shipper":
                intent = new Intent(this, ShipperDashboardActivity.class);
                break;
            case "manager":
                intent = new Intent(this, ManagerDashboardActivity.class);
                break;
            case "support":
                intent = new Intent(this, CustomerSupportDashboardActivity.class);
                break;
            default:
                intent = new Intent(this, CustomerDashboardActivity.class);
                break;
        }
        startActivity(intent);
        finish(); // Close login activity
    }

    private void navigateToRoleActivityFromMock(int roleId) {
        Intent intent;
        switch (roleId) {
            case Role.CUSTOMER:
                intent = new Intent(this, CustomerDashboardActivity.class);
                break;
            case Role.BARISTA:
                intent = new Intent(this, BaristaDashboardActivity.class);
                break;
            case Role.SHIPPER:
                intent = new Intent(this, ShipperDashboardActivity.class);
                break;
            case Role.MANAGER:
                intent = new Intent(this, ManagerDashboardActivity.class);
                break;
            case Role.CUSTOMER_SUPPORT:
                intent = new Intent(this, CustomerSupportDashboardActivity.class);
                break;
            default:
                intent = new Intent(this, CustomerDashboardActivity.class);
                break;
        }
        startActivity(intent);
        finish(); // Close login activity
    }
}
