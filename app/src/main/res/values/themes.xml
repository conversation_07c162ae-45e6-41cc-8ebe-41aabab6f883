<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.CoffeeShop" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <!-- Text colors -->
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <!-- Typography and shape -->
        <item name="textAppearanceHeadline1">@style/TextAppearance.CoffeeShop.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.CoffeeShop.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.CoffeeShop.Headline3</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.CoffeeShop.Subtitle1</item>
        <item name="textAppearanceBody1">@style/TextAppearance.CoffeeShop.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.CoffeeShop.Body2</item>
        <item name="textAppearanceCaption">@style/TextAppearance.CoffeeShop.Caption</item>
        <item name="textAppearanceButton">@style/TextAppearance.CoffeeShop.Button</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.CoffeeShop.SmallComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.CoffeeShop.MediumComponent</item>
    </style>

    <!-- Typography styles -->
    <style name="TextAppearance.CoffeeShop.Headline1" parent="TextAppearance.MaterialComponents.Headline1">
        <item name="fontFamily">sans-serif-light</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:textSize">28sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Headline2" parent="TextAppearance.MaterialComponents.Headline2">
        <item name="fontFamily">sans-serif</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Headline3" parent="TextAppearance.MaterialComponents.Headline3">
        <item name="fontFamily">sans-serif-medium</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Subtitle1" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="fontFamily">sans-serif</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="fontFamily">sans-serif</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="fontFamily">sans-serif</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="fontFamily">sans-serif</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Button" parent="TextAppearance.MaterialComponents.Button">
        <item name="fontFamily">sans-serif-medium</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!-- Shape styles -->
    <style name="ShapeAppearance.CoffeeShop.SmallComponent" parent="ShapeAppearance.MaterialComponents.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>

    <style name="ShapeAppearance.CoffeeShop.MediumComponent" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>
</resources>