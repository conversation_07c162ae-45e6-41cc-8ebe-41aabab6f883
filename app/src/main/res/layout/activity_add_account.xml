<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="4dp"
            app:title="Add New Account"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_revert"
            app:navigationIconTint="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Account Information Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                style="@style/Widget.CoffeeShop.ListItem">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Account Information"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="20dp" />

                    <!-- Username Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_username"
                        style="@style/Widget.CoffeeShop.TextInputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Username"
                        android:layout_marginBottom="16dp"
                        app:counterEnabled="true"
                        app:counterMaxLength="20"
                        app:helperText="Must be unique">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_username"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textNoSuggestions"
                            android:maxLength="20" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Email Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_email"
                        style="@style/Widget.CoffeeShop.TextInputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Email Address"
                        android:layout_marginBottom="16dp"
                        app:helperText="Valid email required">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Phone Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_phone"
                        style="@style/Widget.CoffeeShop.TextInputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Phone Number"
                        android:layout_marginBottom="16dp"
                        app:helperText="Optional">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_phone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="phone" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Role Selection -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_role"
                        style="@style/Widget.CoffeeShop.TextInputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Role"
                        android:layout_marginBottom="16dp"
                        app:endIconMode="dropdown_menu"
                        app:helperText="Select user role">

                        <com.google.android.material.textfield.MaterialAutoCompleteTextView
                            android:id="@+id/spinner_role"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:focusable="false" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Status Selection -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Account Status"
                        android:textAppearance="?attr/textAppearanceSubtitle2"
                        android:textColor="@color/primary"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="12dp" />

                    <RadioGroup
                        android:id="@+id/rg_status"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.radiobutton.MaterialRadioButton
                            android:id="@+id/rb_active"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Active"
                            android:checked="true"
                            android:textColor="@color/primary" />

                        <com.google.android.material.radiobutton.MaterialRadioButton
                            android:id="@+id/rb_inactive"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Inactive"
                            android:textColor="@color/primary" />

                    </RadioGroup>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Password Setup Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                style="@style/Widget.CoffeeShop.ListItem">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Password Setup"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="16dp" />

                    <!-- Password Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_password"
                        style="@style/Widget.CoffeeShop.TextInputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Password"
                        android:layout_marginBottom="16dp"
                        app:endIconMode="password_toggle"
                        app:helperText="Minimum 6 characters">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Confirm Password Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_confirm_password"
                        style="@style/Widget.CoffeeShop.TextInputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Confirm Password"
                        android:layout_marginBottom="16dp"
                        app:endIconMode="password_toggle"
                        app:helperText="Must match password">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_confirm_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="16dp">

                <!-- Create Account Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_create_account"
                    style="@style/Widget.CoffeeShop.Button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Create Account"
                    app:icon="@android:drawable/ic_menu_add"
                    app:iconGravity="textStart" />

                <!-- Cancel Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_cancel"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Cancel"
                    app:icon="@android:drawable/ic_menu_close_clear_cancel"
                    app:iconGravity="textStart"
                    app:strokeColor="@color/primary" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>