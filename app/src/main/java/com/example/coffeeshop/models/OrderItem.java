package com.example.coffeeshop.models;

import java.math.BigDecimal;

public class OrderItem {
    private int orderItemId;
    private int orderId;
    private int itemId;
    private String itemName; // For display purposes
    private int quantity;
    private BigDecimal price;

    // Constructors
    public OrderItem(String itemName, int quantity, double price) {
        this.itemName = itemName;
        this.quantity = quantity;
        this.price = BigDecimal.valueOf(price);
    }

    // Từ main: Constructor đầy đủ (có orderItemId)
    public OrderItem(int orderItemId, int orderId, int itemId, String itemName, int quantity, BigDecimal price) {
        this.orderItemId = orderItemId;
        this.orderId = orderId;
        this.itemId = itemId;
        this.itemName = itemName;
        this.quantity = quantity;
        this.price = price;
    }

    // Từ main: Constructor không có orderItemId
    public OrderItem(int orderId, int itemId, String itemName, int quantity, BigDecimal price) {
        this.orderId = orderId;
        this.itemId = itemId;
        this.itemName = itemName;
        this.quantity = quantity;
        this.price = price;
    }

    // Getters and Setters

    public int getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(int orderItemId) {
        this.orderItemId = orderItemId;
    }

    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public int getItemId() {
        return itemId;
    }

    public void setItemId(int itemId) {
        this.itemId = itemId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    // Nếu cần tương thích kiểu double (từ baovd)
    public double getPriceAsDouble() {
        return price.doubleValue();
    }

    public void setPriceFromDouble(double price) {
        this.price = BigDecimal.valueOf(price);
    }

    // Utility methods
    public BigDecimal getSubtotal() {
        return price.multiply(BigDecimal.valueOf(quantity));
    }

    public String getFormattedPrice() {
        return "$" + price.toString();
    }

    public String getFormattedSubtotal() {
        return "$" + getSubtotal().toString();
    }

    public String getDisplayText() {
        if (quantity > 1) {
            return quantity + "x " + itemName + " (" + getFormattedPrice() + " each)";
        } else {
            return itemName + " - " + getFormattedPrice();
        }
    }

    // Alias method for compatibility
    public String getName() {
        return itemName;
    }
}
