<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f5f5f5">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        android:clipToPadding="false"
        android:paddingBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="20dp">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_back"
                android:contentDescription="Back" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Order Tracking"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="#333"
                android:gravity="center" />

            <View
                android:layout_width="40dp"
                android:layout_height="40dp" />

        </LinearLayout>

        <!-- Order Info Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:id="@+id/tv_order_id"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Order #12345"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    android:textColor="#333"
                    android:gravity="center"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_order_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Status: PREPARING"
                    android:textSize="16sp"
                    android:textColor="#8B4513"
                    android:gravity="center"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tv_order_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Ordered: Jan 15, 2025 at 2:30 PM"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:gravity="center"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_delivery_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Delivery to: 123 Main St, City"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:gravity="center" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Progress Tracking Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Order Progress"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#333"
                    android:layout_marginBottom="20dp" />

                <!-- Progress Bar -->
                <ProgressBar
                    android:id="@+id/progress_bar"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:layout_marginBottom="20dp"
                    android:max="100"
                    android:progress="50"
                    android:progressTint="#8B4513"
                    android:progressBackgroundTint="#e0e0e0" />

                <!-- Status Steps -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="4">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tv_status_pending"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:text="1"
                            android:textColor="@android:color/white"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:background="@drawable/status_active"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Order\nReceived"
                            android:textSize="12sp"
                            android:textColor="#666"
                            android:gravity="center" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tv_status_preparing"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:text="2"
                            android:textColor="@android:color/white"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:background="@drawable/status_pending"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Preparing"
                            android:textSize="12sp"
                            android:textColor="#666"
                            android:gravity="center" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tv_status_on_way"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:text="3"
                            android:textColor="@android:color/white"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:background="@drawable/status_pending"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="On the\nWay"
                            android:textSize="12sp"
                            android:textColor="#666"
                            android:gravity="center" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tv_status_delivered"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:text="4"
                            android:textColor="@android:color/white"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:background="@drawable/status_pending"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Delivered"
                            android:textSize="12sp"
                            android:textColor="#666"
                            android:gravity="center" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="20dp">

            <Button
                android:id="@+id/btn_cancel_order"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="Cancel Order"
                android:textSize="16sp"
                android:textColor="@android:color/white"
                android:background="@drawable/button_danger"
                android:layout_marginBottom="12dp" />

            <Button
                android:id="@+id/btn_back_to_dashboard"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="Back to Dashboard"
                android:textSize="16sp"
                android:textColor="#8B4513"
                android:background="@drawable/button_outline" />

        </LinearLayout>

    </LinearLayout>

    </ScrollView>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/surface"
        app:itemIconTint="@color/primary"
        app:itemTextColor="@color/primary"
        app:menu="@menu/customer_bottom_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
