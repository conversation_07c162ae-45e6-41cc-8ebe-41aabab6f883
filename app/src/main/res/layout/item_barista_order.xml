<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardElevation="4dp"
    app:cardCornerRadius="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Order Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_order_id"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Order #1001"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                tools:text="Order #1001" />

            <TextView
                android:id="@+id/tv_status_badge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="PENDING"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:background="@drawable/status_badge_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                tools:text="PENDING" />

        </LinearLayout>

        <!-- Customer Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_customer_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Customer: John Doe"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:drawableStart="@android:drawable/ic_menu_myplaces"
                android:drawablePadding="8dp"
                android:gravity="center_vertical"
                tools:text="Customer: John Doe" />

            <TextView
                android:id="@+id/tv_order_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5 min ago"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:drawableStart="@android:drawable/ic_menu_recent_history"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                tools:text="5 min ago" />

        </LinearLayout>

        <!-- Order Items -->
        <TextView
            android:id="@+id/tv_items_summary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2x Espresso, 1x Cappuccino"
            android:textSize="14sp"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="8dp"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="2x Espresso, 1x Cappuccino, 1x Chocolate Croissant" />

        <!-- Total Price -->
        <TextView
            android:id="@+id/tv_total_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Total: $15.50"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/primary"
            android:layout_marginBottom="12dp"
            tools:text="Total: $15.50" />

        <!-- Action Button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_update_status"
                style="@style/Widget.MaterialComponents.Button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Start Making"
                android:backgroundTint="@color/primary"
                tools:text="Mark Ready" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
