package com.example.coffeeshop.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.example.coffeeshop.R;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.CartItem;
import com.example.coffeeshop.models.Product;

import java.math.BigDecimal;
import java.util.List;

public class CheckoutItemAdapter extends RecyclerView.Adapter<CheckoutItemAdapter.ViewHolder> {

    private List<CartItem> cartItems;
    private MockDataManager dataManager;

    public CheckoutItemAdapter(List<CartItem> cartItems) {
        this.cartItems = cartItems;
        this.dataManager = MockDataManager.getInstance();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_checkout, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        CartItem cartItem = cartItems.get(position);
        Product product = dataManager.findProductById(cartItem.getProductId());
        
        if (product != null) {
            holder.tvProductName.setText(product.getName());
            holder.tvQuantity.setText("Qty: " + cartItem.getQuantity());
            
            BigDecimal itemTotal = cartItem.getUnitPrice().multiply(new BigDecimal(cartItem.getQuantity()));
            holder.tvPrice.setText("$" + itemTotal.setScale(2, BigDecimal.ROUND_HALF_UP));
            holder.tvUnitPrice.setText("$" + cartItem.getUnitPrice().setScale(2, BigDecimal.ROUND_HALF_UP) + " each");
            
            // Load product image
            Glide.with(holder.itemView.getContext())
                    .load(product.getImageUrl())
                    .placeholder(R.drawable.placeholder_coffee)
                    .error(R.drawable.placeholder_coffee)
                    .into(holder.ivProductImage);
        }
    }

    @Override
    public int getItemCount() {
        return cartItems.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivProductImage;
        TextView tvProductName, tvQuantity, tvPrice, tvUnitPrice;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivProductImage = itemView.findViewById(R.id.iv_product_image);
            tvProductName = itemView.findViewById(R.id.tv_product_name);
            tvQuantity = itemView.findViewById(R.id.tv_quantity);
            tvPrice = itemView.findViewById(R.id.tv_price);
            tvUnitPrice = itemView.findViewById(R.id.tv_unit_price);
        }
    }
}
