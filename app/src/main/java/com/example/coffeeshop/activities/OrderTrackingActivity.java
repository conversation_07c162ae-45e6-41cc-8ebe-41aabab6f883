package com.example.coffeeshop.activities;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.Order;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationBarView;

public class OrderTrackingActivity extends AppCompatActivity {

    private TextView tvOrderId, tvOrderStatus, tvOrderTime, tvDeliveryAddress;
    private TextView tvStatusPending, tvStatusPreparing, tvStatusOnWay, tvStatusDelivered;
    private ProgressBar progressBar;
    private Button btnCancelOrder, btnBackToDashboard;
    private BottomNavigationView bottomNavigation;
    
    private MockDataManager dataManager;
    private Order currentOrder;
    private int orderId;
    private int userId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_order_tracking);
        
        // Get order ID and user ID from intent
        orderId = getIntent().getIntExtra("orderId", -1);
        userId = getIntent().getIntExtra("userId", 1);
        
        dataManager = MockDataManager.getInstance();
        initViews();
        loadOrderDetails();
        setupListeners();
        setupBottomNavigation();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadOrderDetails(); // Refresh order status
    }

    private void initViews() {
        tvOrderId = findViewById(R.id.tv_order_id);
        tvOrderStatus = findViewById(R.id.tv_order_status);
        tvOrderTime = findViewById(R.id.tv_order_time);
        tvDeliveryAddress = findViewById(R.id.tv_delivery_address);
        
        tvStatusPending = findViewById(R.id.tv_status_pending);
        tvStatusPreparing = findViewById(R.id.tv_status_preparing);
        tvStatusOnWay = findViewById(R.id.tv_status_on_way);
        tvStatusDelivered = findViewById(R.id.tv_status_delivered);
        
        progressBar = findViewById(R.id.progress_bar);
        btnCancelOrder = findViewById(R.id.btn_cancel_order);
        btnBackToDashboard = findViewById(R.id.btn_back_to_dashboard);
        bottomNavigation = findViewById(R.id.bottom_navigation);
    }

    private void loadOrderDetails() {
        currentOrder = dataManager.findOrderById(orderId);
        
        if (currentOrder == null) {
            Toast.makeText(this, "Order not found", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        updateOrderInfo();
        updateProgressBar();
    }

    private void updateOrderInfo() {
        tvOrderId.setText("Order #" + currentOrder.getOrderId());
        tvOrderStatus.setText("Status: " + currentOrder.getStatus().toUpperCase());
        tvOrderTime.setText("Ordered: " + currentOrder.getFormattedCreatedAt());
        tvDeliveryAddress.setText("Delivery to: " + currentOrder.getDeliveryAddress());
    }

    private void updateProgressBar() {
        String status = currentOrder.getStatus().toLowerCase();
        
        // Reset all status indicators
        resetStatusIndicators();
        
        switch (status) {
            case "pending":
                progressBar.setProgress(25);
                tvStatusPending.setTextColor(getResources().getColor(R.color.primary));
                tvStatusPending.setBackgroundResource(R.drawable.status_active);
                break;
            case "preparing":
                progressBar.setProgress(50);
                tvStatusPending.setTextColor(getResources().getColor(R.color.accent));
                tvStatusPending.setBackgroundResource(R.drawable.status_completed);
                tvStatusPreparing.setTextColor(getResources().getColor(R.color.primary));
                tvStatusPreparing.setBackgroundResource(R.drawable.status_active);
                break;
            case "on_way":
            case "on the way":
                progressBar.setProgress(75);
                tvStatusPending.setTextColor(getResources().getColor(R.color.accent));
                tvStatusPending.setBackgroundResource(R.drawable.status_completed);
                tvStatusPreparing.setTextColor(getResources().getColor(R.color.accent));
                tvStatusPreparing.setBackgroundResource(R.drawable.status_completed);
                tvStatusOnWay.setTextColor(getResources().getColor(R.color.primary));
                tvStatusOnWay.setBackgroundResource(R.drawable.status_active);
                break;
            case "delivered":
            case "finished":
                progressBar.setProgress(100);
                tvStatusPending.setTextColor(getResources().getColor(R.color.accent));
                tvStatusPending.setBackgroundResource(R.drawable.status_completed);
                tvStatusPreparing.setTextColor(getResources().getColor(R.color.accent));
                tvStatusPreparing.setBackgroundResource(R.drawable.status_completed);
                tvStatusOnWay.setTextColor(getResources().getColor(R.color.accent));
                tvStatusOnWay.setBackgroundResource(R.drawable.status_completed);
                tvStatusDelivered.setTextColor(getResources().getColor(R.color.accent));
                tvStatusDelivered.setBackgroundResource(R.drawable.status_completed);
                btnCancelOrder.setEnabled(false);
                btnCancelOrder.setText("Order Completed");
                break;
            case "cancelled":
                progressBar.setProgress(0);
                tvOrderStatus.setText("Status: CANCELLED");
                tvOrderStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                btnCancelOrder.setEnabled(false);
                btnCancelOrder.setText("Order Cancelled");
                break;
        }
        
        // Hide cancel button if order is finished or delivered
        if ("finished".equals(status) || "delivered".equals(status) || "cancelled".equals(status)) {
            btnCancelOrder.setEnabled(false);
        }
    }

    private void resetStatusIndicators() {
        int defaultColor = getResources().getColor(android.R.color.darker_gray);
        int defaultBackground = R.drawable.status_pending;
        
        tvStatusPending.setTextColor(defaultColor);
        tvStatusPending.setBackgroundResource(defaultBackground);
        tvStatusPreparing.setTextColor(defaultColor);
        tvStatusPreparing.setBackgroundResource(defaultBackground);
        tvStatusOnWay.setTextColor(defaultColor);
        tvStatusOnWay.setBackgroundResource(defaultBackground);
        tvStatusDelivered.setTextColor(defaultColor);
        tvStatusDelivered.setBackgroundResource(defaultBackground);
    }

    private void setupListeners() {
        btnCancelOrder.setOnClickListener(v -> showCancelOrderDialog());
        btnBackToDashboard.setOnClickListener(v -> {
            Intent intent = new Intent(this, CustomerDashboardActivity.class);
            intent.putExtra("userId", userId);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
            finish();
        });
        
        findViewById(R.id.btn_back).setOnClickListener(v -> finish());
    }

    private void showCancelOrderDialog() {
        new AlertDialog.Builder(this)
                .setTitle("Cancel Order")
                .setMessage("Are you sure you want to cancel this order? This action cannot be undone.")
                .setPositiveButton("Yes, Cancel", (dialog, which) -> cancelOrder())
                .setNegativeButton("No", null)
                .show();
    }

    private void cancelOrder() {
        boolean success = dataManager.updateOrderStatus(orderId, "cancelled");
        
        if (success) {
            Toast.makeText(this, "Order cancelled successfully", Toast.LENGTH_SHORT).show();
            loadOrderDetails(); // Refresh to show cancelled status
        } else {
            Toast.makeText(this, "Failed to cancel order", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void setupBottomNavigation() {
        if (bottomNavigation == null) return;

        bottomNavigation.setSelectedItemId(R.id.nav_orders);
        bottomNavigation.setOnItemSelectedListener(new NavigationBarView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_home) {
                    // Navigate to home
                    Intent intent = new Intent(OrderTrackingActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", userId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_menu) {
                    // Navigate to menu (home)
                    Intent intent = new Intent(OrderTrackingActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", userId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    // Already on orders screen
                    return true;
                } else if (itemId == R.id.nav_favorites) {
                    // Show a simple message since favorites isn't implemented
                    Toast.makeText(OrderTrackingActivity.this, "Favorites - Coming Soon!", Toast.LENGTH_SHORT).show();
                    return true;
                } else if (itemId == R.id.nav_profile) {
                    // Navigate to Profile
                    Intent intent = new Intent(OrderTrackingActivity.this, ProfileActivity.class);
                    intent.putExtra("userId", userId);
                    startActivity(intent);
                    finish();
                    return true;
                }
                return false;
            }
        });
    }
}
