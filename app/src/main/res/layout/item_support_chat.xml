<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">
    <TextView
        android:id="@+id/textChatSender"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Sender"
        android:textStyle="bold"
        android:textSize="12sp"/>
    <TextView
        android:id="@+id/textChatMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Message"
        android:background="@android:color/darker_gray"
        android:textColor="@android:color/white"
        android:padding="8dp"
        android:textSize="16sp"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="2dp"/>
    <TextView
        android:id="@+id/textChatTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Time"
        android:textSize="10sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginBottom="4dp"/>
</LinearLayout>

