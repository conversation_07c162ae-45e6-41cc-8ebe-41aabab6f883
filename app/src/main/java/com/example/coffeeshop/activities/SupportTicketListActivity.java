package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.content.Intent;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.coffeeshop.data.MockDataManagerSupport;
import com.example.coffeeshop.models.SupportTicket;
import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.SupportTicketListAdapter;
import java.util.List;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Button;
import java.util.ArrayList;
import java.util.Locale;
import android.view.View;

public class SupportTicketListActivity extends AppCompatActivity {
    private RecyclerView recyclerView;
    private SupportTicketListAdapter adapter;
    private List<SupportTicket> tickets;
    private int currentUserId = 1;
    private EditText editFilterId, editFilterName;
    private Spinner spinnerStatus, spinnerPriority;
    private Button btnExport;
    private List<SupportTicket> allTickets;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_support_ticket_list);
        currentUserId = getIntent().getIntExtra("userId", 1);
        recyclerView = findViewById(R.id.recyclerViewSupportTickets);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        editFilterId = findViewById(R.id.editFilterId);
        editFilterName = findViewById(R.id.editFilterName);
        spinnerStatus = findViewById(R.id.spinnerStatus);
        spinnerPriority = findViewById(R.id.spinnerPriority);
        btnExport = findViewById(R.id.btnExport);
        // Populate spinners
        spinnerStatus.setAdapter(new android.widget.ArrayAdapter<>(this, android.R.layout.simple_spinner_dropdown_item, new String[]{"All", "Open", "In Progress", "Resolved", "Escalated"}));
        spinnerPriority.setAdapter(new android.widget.ArrayAdapter<>(this, android.R.layout.simple_spinner_dropdown_item, new String[]{"All", "Low", "Medium", "High", "Urgent"}));
        // Get all tickets (for staff, show all, for customer, filter by id)
        allTickets = MockDataManagerSupport.getInstance().getTicketsForCustomer(currentUserId);
        tickets = new ArrayList<>(allTickets);
        adapter = new SupportTicketListAdapter(tickets, new SupportTicketListAdapter.OnTicketActionListener() {
            @Override
            public void onViewDetail(SupportTicket ticket) {
                Intent intent = new Intent(SupportTicketListActivity.this, SupportTicketDetailActivity.class);
                intent.putExtra("ticketId", ticket.id);
                startActivity(intent);
            }
            @Override
            public void onAssignStaff(SupportTicket ticket) {
                // Get current staff name (mock: from User, in real app use logged-in user)
                String staffName = getCurrentStaffName();
                // Assign 'You' for current user, but store real name for other staff
                com.example.coffeeshop.data.MockDataManagerSupport.getInstance().assignTicketToStaff(ticket.id, staffName);
                android.widget.Toast.makeText(SupportTicketListActivity.this, "Assigned to you!", android.widget.Toast.LENGTH_SHORT).show();
                adapter.notifyDataSetChanged();
            }
        });
        recyclerView.setAdapter(adapter);
        // Filter logic
        android.text.TextWatcher filterWatcher = new android.text.TextWatcher() {
            @Override public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            @Override public void onTextChanged(CharSequence s, int start, int before, int count) { filterTickets(); }
            @Override public void afterTextChanged(android.text.Editable s) { filterTickets(); }
        };
        editFilterId.addTextChangedListener(filterWatcher);
        editFilterName.addTextChangedListener(filterWatcher);
        spinnerStatus.setOnItemSelectedListener(new android.widget.AdapterView.OnItemSelectedListener() {
            @Override public void onItemSelected(android.widget.AdapterView<?> parent, View view, int position, long id) { filterTickets(); }
            @Override public void onNothingSelected(android.widget.AdapterView<?> parent) {}
        });
        spinnerPriority.setOnItemSelectedListener(new android.widget.AdapterView.OnItemSelectedListener() {
            @Override public void onItemSelected(android.widget.AdapterView<?> parent, View view, int position, long id) { filterTickets(); }
            @Override public void onNothingSelected(android.widget.AdapterView<?> parent) {}
        });
        btnExport.setOnClickListener(v -> exportTickets());
    }
    @Override
    protected void onResume() {
        super.onResume();
        // Always reload tickets and refresh the list for the latest status
        allTickets.clear();
        allTickets.addAll(MockDataManagerSupport.getInstance().getTicketsForCustomer(currentUserId));
        filterTickets(); // This will update the filtered list and notify the adapter
    }
    private void filterTickets() {
        String idText = editFilterId.getText().toString().trim();
        String nameText = editFilterName.getText().toString().toLowerCase(Locale.ROOT);
        String status = spinnerStatus.getSelectedItem().toString();
        String priority = spinnerPriority.getSelectedItem().toString();
        tickets.clear();
        for (SupportTicket t : allTickets) {
            boolean match = true;
            if (!idText.isEmpty() && !(""+t.id).contains(idText)) match = false;
            if (!nameText.isEmpty() && (t.customerName == null || !t.customerName.toLowerCase(Locale.ROOT).contains(nameText))) match = false;
            if (!status.equals("All") && !t.status.equals(status)) match = false;
            if (!priority.equals("All") && !t.priority.equals(priority)) match = false;
            if (match) tickets.add(t);
        }
        adapter.notifyDataSetChanged();
    }
    private void exportTickets() {
        // Mock: just show a toast
        android.widget.Toast.makeText(this, "Exported " + tickets.size() + " tickets!", android.widget.Toast.LENGTH_SHORT).show();
    }
    // Mock: get current staff name (replace with real user session in production)
    private String getCurrentStaffName() {
        // In a real app, get from logged-in User object/session
        // For demo, return 'You' for current user, but you can return the real name for other staff
        return "You";
    }
}
