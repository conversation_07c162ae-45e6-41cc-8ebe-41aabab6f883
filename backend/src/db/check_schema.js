const db = require('./init');

console.log('Checking database schema...');

// Get table structure
db.all("PRAGMA table_info(users)", (err, columns) => {
    if (err) {
        console.error('Error getting table info:', err);
    } else {
        console.log('Users table columns:');
        columns.forEach(col => {
            console.log(`- ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.pk ? 'PRIMARY KEY' : ''}`);
        });
    }
    
    // Also check what users exist
    db.all("SELECT user_id, full_name, email FROM users LIMIT 5", (err, users) => {
        if (err) {
            console.error('Error getting users:', err);
        } else {
            console.log('\nExisting users:');
            if (users.length === 0) {
                console.log('No users found in database');
            } else {
                users.forEach(user => {
                    console.log(`- ${user.user_id}: ${user.full_name} (${user.email})`);
                });
            }
        }
        process.exit(0);
    });
});
