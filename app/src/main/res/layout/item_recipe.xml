<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_recipe"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_recipe_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Espresso"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/tv_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Espresso"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:background="@drawable/rounded_background"
                android:backgroundTint="@color/accent_color"
                android:padding="4dp"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- Description -->
        <TextView
            android:id="@+id/tv_recipe_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Classic Italian espresso with perfect crema"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="12dp"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- Info Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Time -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginEnd="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="⏱"
                    android:textSize="16sp"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/tv_preparation_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3 min"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

            <!-- Difficulty -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginEnd="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📊"
                    android:textSize="16sp"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/tv_difficulty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Medium"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Steps -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical|end">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📋"
                    android:textSize="16sp"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/tv_step_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="5 steps"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
