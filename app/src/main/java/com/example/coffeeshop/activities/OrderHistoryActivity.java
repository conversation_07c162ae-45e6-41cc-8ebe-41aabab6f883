package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.OrderHistoryAdapter;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.Order;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationBarView;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class OrderHistoryActivity extends AppCompatActivity implements OrderHistoryAdapter.OnOrderHistoryActionListener {

    private RecyclerView rvOrderHistory;
    private SwipeRefreshLayout swipeRefresh;
    private View layoutEmptyState;
    private TextView tvEmptyMessage;
    private BottomNavigationView bottomNavigation;
    
    private MockDataManager dataManager;
    private OrderHistoryAdapter adapter;
    private List<Order> orderHistory;
    private int currentUserId = 1; // Default customer

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_order_history);
        
        // Get user ID from intent
        currentUserId = getIntent().getIntExtra("userId", 1);
        
        dataManager = MockDataManager.getInstance();
        initViews();
        setupRecyclerView();
        setupSwipeRefresh();
        setupBottomNavigation();
        loadOrderHistory();
        setupListeners();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadOrderHistory(); // Refresh when returning to this screen
    }

    private void initViews() {
        rvOrderHistory = findViewById(R.id.rv_order_history);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        layoutEmptyState = findViewById(R.id.layout_empty_state);
        tvEmptyMessage = findViewById(R.id.tv_empty_message);
        bottomNavigation = findViewById(R.id.bottom_navigation);
    }

    private void setupRecyclerView() {
        orderHistory = new ArrayList<>();
        adapter = new OrderHistoryAdapter(orderHistory, this);
        rvOrderHistory.setLayoutManager(new LinearLayoutManager(this));
        rvOrderHistory.setAdapter(adapter);
    }

    private void setupSwipeRefresh() {
        swipeRefresh.setColorSchemeResources(
            R.color.primary,
            R.color.secondary,
            R.color.accent
        );
        
        swipeRefresh.setOnRefreshListener(() -> {
            loadOrderHistory();
            swipeRefresh.setRefreshing(false);
            Toast.makeText(this, "Order history refreshed!", Toast.LENGTH_SHORT).show();
        });
    }

    private void setupListeners() {
        findViewById(R.id.btn_back).setOnClickListener(v -> finish());
    }

    private void loadOrderHistory() {
        // Get orders from last 3 months for current user
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -3);
        Date threeMonthsAgo = calendar.getTime();
        orderHistory = dataManager.getOrderHistoryByUserIdSince(currentUserId, threeMonthsAgo);
        
        if (adapter != null) {
            adapter.updateOrderHistory(orderHistory);
        }
        
        updateEmptyState();
    }

    private void updateEmptyState() {
        if (orderHistory.isEmpty()) {
            layoutEmptyState.setVisibility(View.VISIBLE);
            rvOrderHistory.setVisibility(View.GONE);
            tvEmptyMessage.setText("No orders found in the last 3 months");
        } else {
            layoutEmptyState.setVisibility(View.GONE);
            rvOrderHistory.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onViewOrderDetails(Order order) {
        // Navigate to order details or show dialog
        Intent intent = new Intent(this, OrderTrackingActivity.class);
        intent.putExtra("orderId", order.getOrderId());
        intent.putExtra("userId", currentUserId);
        startActivity(intent);
    }

    @Override
    public void onReorderItems(Order order) {
        boolean success = dataManager.reorderItems(order.getOrderId(), currentUserId);
        
        if (success) {
            Toast.makeText(this, "Items added to cart! Items from order #" + order.getOrderId(), Toast.LENGTH_LONG).show();
        } else {
            Toast.makeText(this, "Failed to reorder items", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void setupBottomNavigation() {
        if (bottomNavigation == null) return;

        bottomNavigation.setSelectedItemId(R.id.nav_orders);
        bottomNavigation.setOnItemSelectedListener(new NavigationBarView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_home) {
                    // Navigate to home
                    Intent intent = new Intent(OrderHistoryActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_menu) {
                    // Navigate to menu (home)
                    Intent intent = new Intent(OrderHistoryActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    // Already on orders screen
                    return true;
                } else if (itemId == R.id.nav_favorites) {
                    // Show a simple message since favorites isn't implemented
                    Toast.makeText(OrderHistoryActivity.this, "Favorites - Coming Soon!", Toast.LENGTH_SHORT).show();
                    return true;
                } else if (itemId == R.id.nav_profile) {
                    // Navigate to Profile
                    Intent intent = new Intent(OrderHistoryActivity.this, ProfileActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                }
                return false;
            }
        });
    }
}
