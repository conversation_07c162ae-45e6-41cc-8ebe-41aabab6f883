<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="?android:colorBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Ti<PERSON>u đề -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📦 Assigned Orders"
            android:textAppearance="?attr/textAppearanceHeadline2" />

        <!-- <PERSON><PERSON><PERSON> hàng ảo 1 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="vertical"
            android:background="@android:color/white"
            android:padding="16dp"
            android:elevation="4dp">

            <TextView
                android:text="Order ID: ORD001"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:text="Customer: John Doe"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:text="Address: 123 Main Street"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <TextView
                android:text="Accepted"
                android:textColor="#008000"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <Button
                android:id="@+id/btn_details"
                android:text="Details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                style="@style/Widget.CoffeeShop.Button" />
        </LinearLayout>

        <!-- Đơn hàng ảo 2 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="vertical"
            android:background="@android:color/white"
            android:padding="16dp"
            android:elevation="4dp">

            <TextView
                android:text="Order ID: ORD002"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:text="Customer: Alice Smith"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:text="Address: 456 Coffee Lane"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <TextView
                android:text="Rejected"
                android:textColor="#8B0000"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <Button
                android:id="@+id/btn_details_2"
                android:text="Details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                style="@style/Widget.CoffeeShop.Button" />
        </LinearLayout>


    </LinearLayout>
</ScrollView>
