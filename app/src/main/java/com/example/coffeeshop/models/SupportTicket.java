package com.example.coffeeshop.models;

import java.util.List;

public class SupportTicket {
    public int id;
    public int customerId;
    public String customerName;
    public String subject;
    public String description;
    public String status; // e.g., Open, In Progress, Resolved, Escalated
    public String priority; // e.g., Low, Medium, High, Urgent
    public List<SupportResponse> responses;
    public int relatedOrderId;
    public String createdAt;
    public String updatedAt;
    public String assignedStaffName; // null if unassigned

    public SupportTicket(int id, int customerId, String customerName, String subject, String description, String status, String priority, List<SupportResponse> responses, int relatedOrderId, String createdAt, String updatedAt) {
        this.id = id;
        this.customerId = customerId;
        this.customerName = customerName;
        this.subject = subject;
        this.description = description;
        this.status = status;
        this.priority = priority;
        this.responses = responses;
        this.relatedOrderId = relatedOrderId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.assignedStaffName = null;
    }
}
