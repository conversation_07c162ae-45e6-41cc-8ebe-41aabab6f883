<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/surface"
    android:elevation="2dp"
    android:orientation="vertical"
    android:padding="12dp"
    android:layout_marginBottom="8dp">

    <!-- Order Info -->
    <TextView
        android:id="@+id/tv_order_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Order #12345"
        android:textAppearance="?attr/textAppearanceSubtitle1" />

    <TextView
        android:id="@+id/tv_customer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Customer: <PERSON>"
        android:textAppearance="?attr/textAppearanceBody2" />

    <!-- Details Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_details"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Details"
        android:textColor="?attr/colorOnPrimary"
        android:backgroundTint="?attr/colorPrimary"
        android:layout_marginTop="12dp"
        app:cornerRadius="12dp"
        style="@style/Widget.MaterialComponents.Button"
        android:layout_gravity="end" />

</LinearLayout>
