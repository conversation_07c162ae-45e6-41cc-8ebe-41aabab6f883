package com.example.coffeeshop.activities;

import android.os.Bundle;

import androidx.fragment.app.FragmentActivity;

import com.example.coffeeshop.R;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.MarkerOptions;

public class ShipperViewMapActivity extends FragmentActivity implements OnMapReadyCallback {

    private GoogleMap mMap;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_view_map);

        // Lấy fragment bản đồ và thiết lập callback khi sẵn sàng
        SupportMapFragment mapFragment = (SupportMapFragment) getSupportFragmentManager()
                .findFragmentById(R.id.map_fragment);
        if (mapFragment != null) {
            mapFragment.getMapAsync(this);
        }
    }

    @Override
    public void onMapReady(GoogleMap googleMap) {
        mMap = googleMap;

        // Vị trí mẫu (TP.HCM)
        LatLng sampleLocation = new LatLng(10.762622, 106.660172);
        mMap.addMarker(new MarkerOptions().position(sampleLocation).title("Delivery Location"));
        mMap.moveCamera(CameraUpdateFactory.newLatLngZoom(sampleLocation, 15));
    }
}
