# Coffee Shop Management System - Backend API

This is the backend API for the Coffee Shop Management System. It provides all the necessary endpoints to support the Android frontend application.

## Technologies Used

- Node.js
- Express.js
- SQLite3
- bcryptjs for password hashing
- CORS for cross-origin requests
- dotenv for environment configuration

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Create a `.env` file in the root directory with the following content:
   ```
   PORT=3000
   NODE_ENV=development
   ```

3. Initialize the database:
   ```bash
   npm run init-db
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

## API Endpoints

### Authentication

- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration

### Products

- `GET /api/products` - Get all products
- `GET /api/products/categories` - Get product categories
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create new product (Manager only)
- `PUT /api/products/:id` - Update product (Manager only)
- `DELETE /api/products/:id` - Delete product (Manager only)

### Cart

- `GET /api/cart` - Get user's cart items
- `POST /api/cart` - Add item to cart
- `PUT /api/cart/:itemId` - Update cart item quantity
- `DELETE /api/cart/:itemId` - Remove item from cart
- `DELETE /api/cart` - Clear cart

### Orders

- `GET /api/orders` - Get orders (filtered by role and status)
- `GET /api/orders/:id` - Get single order
- `POST /api/orders` - Create new order
- `PUT /api/orders/:id/status` - Update order status (Barista/Manager/Shipper)

### Inventory

- `GET /api/inventory` - Get all inventory items (Barista & Manager)
- `GET /api/inventory/low-stock` - Get low stock items
- `GET /api/inventory/out-of-stock` - Get out of stock items
- `PUT /api/inventory/:id/stock` - Update item stock
- `POST /api/inventory` - Add new inventory item (Manager only)
- `PUT /api/inventory/:id` - Update inventory item (Manager only)
- `DELETE /api/inventory/:id` - Delete inventory item (Manager only)

### Daily Tasks

- `GET /api/tasks` - Get all tasks (Barista & Manager)
- `GET /api/tasks/stats` - Get task statistics
- `POST /api/tasks` - Create new task (Manager only)
- `PUT /api/tasks/:id/toggle` - Toggle task completion
- `PUT /api/tasks/:id` - Update task details (Manager only)
- `DELETE /api/tasks/:id` - Delete task (Manager only)

### Recipes

- `GET /api/recipes` - Get all recipes (Barista & Manager)
- `GET /api/recipes/categories` - Get recipe categories
- `GET /api/recipes/:id` - Get single recipe
- `POST /api/recipes` - Create new recipe (Manager only)
- `PUT /api/recipes/:id` - Update recipe (Manager only)
- `DELETE /api/recipes/:id` - Delete recipe (Manager only)

### Support

- `GET /api/support` - Get all tickets (Support & Manager)
- `GET /api/support/my-tickets` - Get user's tickets (Customer)
- `GET /api/support/:id` - Get single ticket
- `POST /api/support` - Create ticket (Customer)
- `POST /api/support/:id/responses` - Add response to ticket
- `PUT /api/support/:id/status` - Update ticket status (Support & Manager)
- `PUT /api/support/:id/priority` - Update ticket priority (Support & Manager)

## Authentication

The API uses basic authentication with email and password. Send these credentials in the request headers:

```
email: <EMAIL>
password: userpassword
```

## Role IDs

- 1: Customer
- 2: Barista
- 3: Shipper
- 4: Manager
- 5: Customer Support

## Development

For development, use:
```bash
npm run dev
```

This will start the server with nodemon for automatic reloading on file changes.

## Database

The application uses SQLite3 for data storage. The database file is created at `src/db/coffee_shop.db`. The schema is defined in `src/db/schema.sql` and includes tables for:

- Users
- Products
- Orders
- Order Items
- Cart Items
- Inventory Items
- Daily Tasks
- Recipes
- Recipe Steps
- Support Tickets
- Support Responses

## Error Handling

All endpoints return responses in the format:

```json
{
    "success": true/false,
    "message": "Success/error message",
    "data": {} // Optional data object
}
```

HTTP status codes are used appropriately:
- 200: Success
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Server Error 