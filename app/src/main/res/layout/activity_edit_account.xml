<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="4dp"
            app:title="Edit Account"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_revert"
            app:navigationIconTint="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- User Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                style="@style/Widget.CoffeeShop.ListItem">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Account Information"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="20dp" />

                    <!-- Username Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_username"
                        style="@style/Widget.CoffeeShop.TextInputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Username"
                        android:layout_marginBottom="16dp"
                        app:counterEnabled="true"
                        app:counterMaxLength="20">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_username"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textNoSuggestions"
                            android:maxLength="20" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Email Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_email"
                        style="@style/Widget.CoffeeShop.TextInputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Email Address"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Phone Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_phone"
                        style="@style/Widget.CoffeeShop.TextInputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Phone Number"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_phone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="phone" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Role Selection (Enhanced Dropdown) -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_role"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Select Role"
                        android:layout_marginBottom="16dp"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:startIconDrawable="@android:drawable/ic_menu_manage"
                        app:startIconTint="@color/primary"
                        app:endIconTint="@color/primary">

                        <com.google.android.material.textfield.MaterialAutoCompleteTextView
                            android:id="@+id/spinner_role"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:focusable="false"
                            android:textColor="?android:attr/textColorPrimary"
                            android:textSize="16sp"
                            android:paddingStart="12dp"
                            android:paddingEnd="12dp"
                            tools:text="Barista" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Status Selection (Enhanced Radio Buttons) -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Account Status"
                        android:textAppearance="?attr/textAppearanceSubtitle2"
                        android:textColor="@color/primary"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp"
                        android:layout_marginTop="8dp"
                        android:drawableStart="@android:drawable/ic_menu_info_details"
                        android:drawableTint="@color/primary"
                        android:drawablePadding="8dp"
                        android:gravity="center_vertical" />

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="2dp"
                        app:strokeColor="@color/primary"
                        app:strokeWidth="1dp">

                        <RadioGroup
                            android:id="@+id/rg_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <com.google.android.material.radiobutton.MaterialRadioButton
                                android:id="@+id/rb_active"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Active"
                                android:textAppearance="?attr/textAppearanceBody1"
                                android:layout_marginBottom="8dp"
                                android:buttonTint="@color/primary"
                                android:paddingStart="8dp"
                                android:drawableEnd="@android:drawable/ic_menu_info_details"
                                android:drawableTint="@android:color/holo_green_dark" />

                            <com.google.android.material.radiobutton.MaterialRadioButton
                                android:id="@+id/rb_inactive"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Inactive"
                                android:textAppearance="?attr/textAppearanceBody1"
                                android:buttonTint="@color/primary"
                                android:paddingStart="8dp"
                                android:drawableEnd="@android:drawable/ic_menu_info_details"
                                android:drawableTint="@android:color/holo_red_dark" />

                        </RadioGroup>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Account Actions Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                style="@style/Widget.CoffeeShop.ListItem">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Account Actions"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="16dp" />

                    <!-- Reset Password Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_reset_password"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="Reset Password"
                        app:icon="@android:drawable/ic_lock_lock"
                        app:iconGravity="textStart"
                        app:strokeColor="@color/primary" />

                    <!-- Last Login Info -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="1dp"
                        app:cardBackgroundColor="@color/surface">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:src="@android:drawable/ic_menu_recent_history"
                                app:tint="@color/primary"
                                android:layout_marginEnd="12dp" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Last Login:"
                                android:textAppearance="?attr/textAppearanceBody2"
                                android:textColor="?android:attr/textColorSecondary" />

                            <TextView
                                android:id="@+id/tv_last_login"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Today, 2:30 PM"
                                android:textAppearance="?attr/textAppearanceBody2"
                                android:textStyle="bold"
                                android:textColor="@color/primary" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="16dp">

                <!-- Save Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_edit"
                    style="@style/Widget.CoffeeShop.Button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Save Changes"
                    app:icon="@android:drawable/ic_menu_save"
                    app:iconGravity="textStart" />

                <!-- Cancel Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_cancel"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Cancel"
                    app:icon="@android:drawable/ic_menu_close_clear_cancel"
                    app:iconGravity="textStart"
                    app:strokeColor="@color/primary" />

                <!-- Delete Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_delete"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Delete Account"
                    android:textColor="@color/error"
                    app:icon="@android:drawable/ic_menu_delete"
                    app:iconGravity="textStart"
                    app:iconTint="@color/error"
                    app:strokeColor="@color/error" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>