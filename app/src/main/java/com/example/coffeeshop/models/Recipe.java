package com.example.coffeeshop.models;

import java.util.Date;
import java.util.List;

public class Recipe {
    private int recipeId;
    private String name;
    private String description;
    private Date createdAt;
    private List<RecipeStep> steps;
    private String difficulty; // Easy, Medium, Hard
    private int preparationTimeMinutes;
    private String category; // E<PERSON>resso, Pour Over, Cold Brew, etc.
    private List<String> ingredients;
    private int productId; // Associated product

    // Constructors
    public Recipe() {}

    public Recipe(int recipeId, String name, String description, Date createdAt, List<RecipeStep> steps) {
        this.recipeId = recipeId;
        this.name = name;
        this.description = description;
        this.createdAt = createdAt;
        this.steps = steps;
        
        // Set defaults based on recipe name
        this.category = determineCategory(name);
        this.difficulty = "Medium";
        this.preparationTimeMinutes = calculateTotalTime();
    }

    public Recipe(int recipeId, String name, String description, String difficulty, 
                  int preparationTimeMinutes, String category) {
        this.recipeId = recipeId;
        this.name = name;
        this.description = description;
        this.difficulty = difficulty;
        this.preparationTimeMinutes = preparationTimeMinutes;
        this.category = category;
        this.createdAt = new Date();
    }

    // Getters and Setters
    public int getRecipeId() {
        return recipeId;
    }

    public void setRecipeId(int recipeId) {
        this.recipeId = recipeId;
    }

    public int getId() {
        return recipeId; // Alias for compatibility
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public List<RecipeStep> getSteps() {
        return steps;
    }

    public void setSteps(List<RecipeStep> steps) {
        this.steps = steps;
    }

    public String getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }

    public int getPreparationTimeMinutes() {
        return preparationTimeMinutes;
    }

    public void setPreparationTimeMinutes(int preparationTimeMinutes) {
        this.preparationTimeMinutes = preparationTimeMinutes;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public List<String> getIngredients() {
        return ingredients;
    }

    public void setIngredients(List<String> ingredients) {
        this.ingredients = ingredients;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    // Utility methods
    public String getFormattedTime() {
        return preparationTimeMinutes + " min";
    }

    public String getDifficultyColor() {
        switch (difficulty != null ? difficulty.toLowerCase() : "medium") {
            case "easy":
                return "#4CAF50"; // Green
            case "medium":
                return "#FF9800"; // Orange
            case "hard":
                return "#F44336"; // Red
            default:
                return "#757575"; // Grey
        }
    }

    public int getStepCount() {
        return steps != null ? steps.size() : 0;
    }

    private String determineCategory(String recipeName) {
        if (recipeName == null) return "Other";
        
        String name = recipeName.toLowerCase();
        if (name.contains("espresso") || name.contains("cappuccino") || name.contains("latte") || name.contains("mocha")) {
            return "Espresso";
        } else if (name.contains("pour") || name.contains("v60") || name.contains("chemex")) {
            return "Pour Over";
        } else if (name.contains("cold") || name.contains("iced")) {
            return "Cold Brew";
        } else if (name.contains("tea") || name.contains("chai")) {
            return "Tea";
        } else if (name.contains("bread") || name.contains("croissant") || name.contains("pastry")) {
            return "Specialty";
        }
        return "Other";
    }

    private int calculateTotalTime() {
        if (steps == null) return 5;
        
        int totalSeconds = 0;
        for (RecipeStep step : steps) {
            totalSeconds += step.getDurationSeconds();
        }
        return Math.max(1, totalSeconds / 60); // Convert to minutes, minimum 1
    }
}
