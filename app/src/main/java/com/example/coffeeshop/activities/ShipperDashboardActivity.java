package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;
import com.google.android.material.button.MaterialButton;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;

public class ShipperDashboardActivity extends AppCompatActivity {

    private TextView tvWelcome;
    private MaterialButton btnLogout, btnAssignedOrders, btnTrackOrders, btnEarnings, btnSchedule, btnAvailable;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_dashboard);

        initViews();
        setupClickListeners();
    }

    private void initViews() {
        tvWelcome = findViewById(R.id.tv_welcome);
        btnLogout = findViewById(R.id.btn_logout);
        btnAssignedOrders = findViewById(R.id.btn_assigned_orders);
        btnTrackOrders = findViewById(R.id.btn_track_orders);
        btnEarnings = findViewById(R.id.btn_earnings);
        btnSchedule = findViewById(R.id.btn_schedule);
        btnAvailable = findViewById(R.id.btn_available);

        tvWelcome.setText("Welcome to CoffeeShipper!\n\nYou are logged in as: SHIPPER");
    }

    private void setupClickListeners() {
        btnAssignedOrders.setOnClickListener(v -> {
            Intent intent = new Intent(ShipperDashboardActivity.this, ShipperViewAssignedOrdersActivity.class);
            startActivity(intent);
        });

        findViewById(R.id.btn_track_orders).setOnClickListener(v -> {
            startActivity(new Intent(this, ShipperTrackOrderActivity.class));
        });


        findViewById(R.id.btn_earnings).setOnClickListener(v -> {
            startActivity(new Intent(this, ShipperEarningsSummaryActivity.class));
        });

        findViewById(R.id.btn_schedule).setOnClickListener(v -> {
            startActivity(new Intent(this, ShipperViewScheduleActivity.class));
        });


        btnAvailable.setOnClickListener(v -> {
        });

        btnLogout.setOnClickListener(v -> logout());
    }

    private void logout() {
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
}
