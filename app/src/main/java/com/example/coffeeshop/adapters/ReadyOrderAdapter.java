package com.example.coffeeshop.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.button.MaterialButton;
import com.example.coffeeshop.R;
import com.example.coffeeshop.models.Order;
import com.example.coffeeshop.models.OrderItem;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

public class ReadyOrderAdapter extends RecyclerView.Adapter<ReadyOrderAdapter.ReadyOrderViewHolder> {
    private List<Order> orders = new ArrayList<>();
    private OnOrderDeliveredListener listener;

    public interface OnOrderDeliveredListener {
        void onOrderDelivered(Order order);
    }

    public ReadyOrderAdapter(OnOrderDeliveredListener listener) {
        this.listener = listener;
    }

    public void updateOrders(List<Order> newOrders) {
        orders.clear();
        orders.addAll(newOrders);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ReadyOrderViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
            .inflate(R.layout.item_ready_order, parent, false);
        return new ReadyOrderViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ReadyOrderViewHolder holder, int position) {
        Order order = orders.get(position);
        holder.bind(order, listener);
    }

    @Override
    public int getItemCount() {
        return orders.size();
    }

    static class ReadyOrderViewHolder extends RecyclerView.ViewHolder {
        private TextView tvOrderId, tvReadyTime, tvCustomerName, tvOrderItems;
        private MaterialButton btnMarkDelivered;

        public ReadyOrderViewHolder(@NonNull View itemView) {
            super(itemView);
            tvOrderId = itemView.findViewById(R.id.tv_order_id);
            tvReadyTime = itemView.findViewById(R.id.tv_ready_time);
            tvCustomerName = itemView.findViewById(R.id.tv_customer_name);
            tvOrderItems = itemView.findViewById(R.id.tv_order_items);
            btnMarkDelivered = itemView.findViewById(R.id.btn_mark_delivered);
        }

        public void bind(Order order, OnOrderDeliveredListener listener) {
            tvOrderId.setText("#" + order.getId());
            tvCustomerName.setText(order.getCustomerName());
            
            // Format order items - use items_summary from API or process items list from mock data
            String itemsText;
            if (order.getItemsSummary() != null && !order.getItemsSummary().isEmpty()) {
                // Use items_summary from API response
                itemsText = order.getItemsSummary();
            } else if (order.getItems() != null && !order.getItems().isEmpty()) {
                // Use individual items from mock data
                itemsText = order.getItems().stream()
                    .map(item -> item.getQuantity() + "x " + item.getName())
                    .collect(Collectors.joining(", "));
            } else {
                itemsText = "No items";
            }
            tvOrderItems.setText(itemsText);
            
            // Calculate ready time (assuming order was marked ready recently)
            tvReadyTime.setText("Just ready");
            
            btnMarkDelivered.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onOrderDelivered(order);
                }
            });
        }
    }
}
