package com.example.coffeeshop.data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MockOrderHistoryProvider {
    public static class OrderHistoryItem {
        public int orderId;
        public int customerId;
        public String date;
        public double total;
        public String status;
        public List<String> items;
        public OrderHistoryItem(int orderId, int customerId, String date, double total, String status, List<String> items) {
            this.orderId = orderId;
            this.customerId = customerId;
            this.date = date;
            this.total = total;
            this.status = status;
            this.items = items;
        }
    }

    private static final List<OrderHistoryItem> MOCK_ORDERS = new ArrayList<>(Arrays.asList(
        new OrderHistoryItem(101, 1, "2025-06-28", 120.5, "Completed", Arrays.asList("Latte", "Croissant")),
        new OrderHistoryItem(102, 1, "2025-06-29", 80.0, "Completed", Arrays.asList("<PERSON><PERSON>resso", "Bagel")),
        new OrderHistoryItem(103, 2, "2025-06-30", 45.0, "Cancelled", Arrays.asList("Americano")),
        new OrderHistoryItem(104, 1, "2025-07-01", 60.0, "Completed", Arrays.asList("Cappuccino", "Muffin")),
        new OrderHistoryItem(105, 3, "2025-06-27", 150.0, "Completed", Arrays.asList("Mocha", "Sandwich", "Juice")),
        new OrderHistoryItem(106, 4, "2025-07-01", 99.0, "Completed", Arrays.asList("Black Coffee", "Brownie"))
    ));

    public static List<OrderHistoryItem> getOrdersForCustomer(int customerId) {
        List<OrderHistoryItem> result = new ArrayList<>();
        for (OrderHistoryItem o : MOCK_ORDERS) if (o.customerId == customerId) result.add(o);
        return result;
    }

    public static List<OrderHistoryItem> getAllOrders() {
        return new ArrayList<>(MOCK_ORDERS);
    }
}
