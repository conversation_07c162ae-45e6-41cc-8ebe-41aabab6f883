package com.example.coffeeshop.activities;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.OrderAdapter;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.Order;
import com.example.coffeeshop.models.OrderItem;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationBarView;
import com.google.android.material.tabs.TabLayout;
import org.json.JSONArray;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class BaristaDashboardActivity extends AppCompatActivity implements OrderAdapter.OnOrderActionListener {

    private TextView tvPendingCount, tvOrdersTitle, tvOrdersCount;
    private TabLayout tabStatusFilter;
    private RecyclerView rvOrders;
    private SwipeRefreshLayout swipeRefresh;
    private View layoutEmptyState;
    private BottomNavigationView bottomNavigation;
    
    private MockDataManager dataManager;
    private ApiService apiService;
    private OrderAdapter orderAdapter;
    private List<Order> allOrders;
    private List<Order> filteredOrders;
    private String currentStatusFilter = "All";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_barista_dashboard);

        dataManager = MockDataManager.getInstance();
        apiService = new ApiService();
        allOrders = new ArrayList<>();
        filteredOrders = new ArrayList<>();
        
        initViews();
        setupStatusFilterTabs();
        setupOrdersList();
        setupSwipeRefresh();
        setupBottomNavigation();
        loadOrders();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadOrders();
    }    private void initViews() {
        tvPendingCount = findViewById(R.id.tv_pending_count);
        tvOrdersTitle = findViewById(R.id.tv_orders_title);
        tvOrdersCount = findViewById(R.id.tv_orders_count);
        tabStatusFilter = findViewById(R.id.tab_status_filter);
        rvOrders = findViewById(R.id.rv_orders);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        layoutEmptyState = findViewById(R.id.layout_empty_state);
        bottomNavigation = findViewById(R.id.bottom_navigation);
        
        // Setup daily tasks click listener
        findViewById(R.id.card_daily_tasks).setOnClickListener(v -> {
            Intent intent = new Intent(BaristaDashboardActivity.this, BaristaDailyTasksActivity.class);
            startActivity(intent);
        });
    }private void setupStatusFilterTabs() {
        String[] statusOptions = {"All", "Pending", "Preparing", "History"};
        
        for (String status : statusOptions) {
            tabStatusFilter.addTab(tabStatusFilter.newTab().setText(status));
        }

        tabStatusFilter.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                if (tab != null && tab.getText() != null) {
                    currentStatusFilter = tab.getText().toString();
                    filterOrdersByStatus();
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {}

            @Override
            public void onTabReselected(TabLayout.Tab tab) {}
        });
    }

    private void setupOrdersList() {
        filteredOrders = new ArrayList<>();
        orderAdapter = new OrderAdapter(this, filteredOrders);
        orderAdapter.setOnOrderActionListener(this);
        
        rvOrders.setLayoutManager(new LinearLayoutManager(this));
        rvOrders.setAdapter(orderAdapter);
    }

    private void setupSwipeRefresh() {
        swipeRefresh.setColorSchemeResources(
            R.color.primary,
            R.color.secondary,
            R.color.accent
        );
        
        swipeRefresh.setOnRefreshListener(() -> {
            loadOrders();
            swipeRefresh.setRefreshing(false);
            Toast.makeText(this, "Orders refreshed!", Toast.LENGTH_SHORT).show();
        });
    }    private void loadOrders() {
        // Load orders from API first, fallback to MockDataManager
        if ("History".equals(currentStatusFilter)) {
            // For history, get all orders and filter completed ones
            apiService.getAllOrders(new ApiService.OrdersCallback() {
                @Override
                public void onSuccess(JSONArray ordersArray) {
                    try {
                        List<Order> allOrdersFromApi = parseOrders(ordersArray);
                        allOrders = filterOrderHistory(allOrdersFromApi);
                        filterOrdersByStatus();
                        updatePendingCount();
                    } catch (Exception e) {
                        e.printStackTrace();
                        loadOrdersFromMock();
                    }
                }

                @Override
                public void onError(String error) {
                    loadOrdersFromMock();
                }
            });
        } else {
            // For active orders, use the active orders endpoint
            apiService.getActiveOrders(new ApiService.OrdersCallback() {
                @Override
                public void onSuccess(JSONArray ordersArray) {
                    try {
                        allOrders = parseOrders(ordersArray);
                        filterOrdersByStatus();
                        updatePendingCount();
                    } catch (Exception e) {
                        e.printStackTrace();
                        loadOrdersFromMock();
                    }
                }

                @Override
                public void onError(String error) {
                    loadOrdersFromMock();
                }
            });
        }
    }
    
    private void loadOrdersFromMock() {
        if ("History".equals(currentStatusFilter)) {
            allOrders = dataManager.getOrderHistory();
        } else {
            allOrders = dataManager.getActiveOrders(); // Only get active orders (excludes finished)
        }
        
        filterOrdersByStatus();
        updatePendingCount();
    }
    
    private List<Order> parseOrders(JSONArray ordersArray) throws Exception {
        List<Order> orders = new ArrayList<>();
        
        for (int i = 0; i < ordersArray.length(); i++) {
            JSONObject orderJson = ordersArray.getJSONObject(i);
            
            // Parse date from API format
            Date orderDate;
            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                orderDate = dateFormat.parse(orderJson.getString("created_at"));
            } catch (Exception e) {
                orderDate = new Date(); // Fallback to current date
            }
            
            Order order = new Order(
                orderJson.getInt("order_id"),
                orderJson.getInt("customer_id"),
                orderJson.getString("customer_name"),
                orderJson.getString("status"),
                new BigDecimal(orderJson.getDouble("total_price")),
                null, // discount
                orderDate
            );
            
            // Set additional fields from API response
            if (orderJson.has("items_summary")) {
                order.setItemsSummary(orderJson.getString("items_summary"));
            }
            
            orders.add(order);
        }
        
        return orders;
    }
    
    private List<Order> filterActiveOrders(List<Order> orders) {
        List<Order> activeOrders = new ArrayList<>();
        for (Order order : orders) {
            String status = order.getStatus().toLowerCase();
            if (!"finished".equals(status) && !"delivered".equals(status) && !"cancelled".equals(status)) {
                activeOrders.add(order);
            }
        }
        return activeOrders;
    }
    
    private List<Order> filterOrderHistory(List<Order> orders) {
        List<Order> historyOrders = new ArrayList<>();
        for (Order order : orders) {
            String status = order.getStatus().toLowerCase();
            if ("finished".equals(status) || "delivered".equals(status) || "cancelled".equals(status)) {
                historyOrders.add(order);
            }
        }
        return historyOrders;
    }    private void filterOrdersByStatus() {
        if ("All".equals(currentStatusFilter)) {
            filteredOrders = new ArrayList<>(allOrders);
            tvOrdersTitle.setText("All Active Orders");
        } else if ("History".equals(currentStatusFilter)) {
            filteredOrders = new ArrayList<>(allOrders); // allOrders already filtered for history in loadOrders()
            tvOrdersTitle.setText("Order History");
        } else {
            // For specific status, filter from current allOrders or load from API
            String statusToFilter = currentStatusFilter.toLowerCase();
            
            // If we have all orders loaded, filter locally
            if (!allOrders.isEmpty()) {
                filteredOrders = new ArrayList<>();
                for (Order order : allOrders) {
                    if (statusToFilter.equals(order.getStatus().toLowerCase())) {
                        filteredOrders.add(order);
                    }
                }
                tvOrdersTitle.setText(currentStatusFilter + " Orders");
                
                if (orderAdapter != null) {
                    orderAdapter.updateOrders(filteredOrders);
                }
                updateOrdersCount();
                updateEmptyState();
            } else {
                // Load specific status from API
                apiService.getOrdersByStatus(statusToFilter, new ApiService.OrdersCallback() {
                    @Override
                    public void onSuccess(JSONArray ordersArray) {
                        try {
                            filteredOrders = parseOrders(ordersArray);
                            tvOrdersTitle.setText(currentStatusFilter + " Orders");
                            
                            if (orderAdapter != null) {
                                orderAdapter.updateOrders(filteredOrders);
                            }
                            updateOrdersCount();
                            updateEmptyState();
                        } catch (Exception e) {
                            e.printStackTrace();
                            filterOrdersByStatusFromMock();
                        }
                    }

                    @Override
                    public void onError(String error) {
                        filterOrdersByStatusFromMock();
                    }
                });
                return; // Early return to avoid duplicate UI updates
            }
        }
        
        if (orderAdapter != null) {
            orderAdapter.updateOrders(filteredOrders);
        }

        updateOrdersCount();
        updateEmptyState();
    }
    
    private void filterOrdersByStatusFromMock() {
        if ("All".equals(currentStatusFilter)) {
            if (allOrders.isEmpty()) {
                allOrders = dataManager.getActiveOrders(); // Reload if empty
            }
            filteredOrders = new ArrayList<>(allOrders);
            tvOrdersTitle.setText("All Active Orders");
        } else if ("History".equals(currentStatusFilter)) {
            filteredOrders = dataManager.getOrderHistory();
            tvOrdersTitle.setText("Order History");
        } else {
            filteredOrders = dataManager.getOrdersByStatus(currentStatusFilter.toLowerCase());
            tvOrdersTitle.setText(currentStatusFilter + " Orders");
        }
        
        if (orderAdapter != null) {
            orderAdapter.updateOrders(filteredOrders);
        }
        
        updateOrdersCount();
        updateEmptyState();
    }    private void updatePendingCount() {
        // Count pending orders from current loaded orders
        int pendingCount = 0;
        for (Order order : allOrders) {
            if ("pending".equals(order.getStatus().toLowerCase())) {
                pendingCount++;
            }
        }
        
        // If no orders loaded yet, fallback to mock data
        if (allOrders.isEmpty()) {
            List<Order> pendingOrders = dataManager.getOrdersByStatus("pending");
            pendingCount = pendingOrders.size();
        }
        
        tvPendingCount.setText(String.valueOf(pendingCount));
        tvPendingCount.setVisibility(pendingCount > 0 ? View.VISIBLE : View.GONE);
    }

    private void updateOrdersCount() {
        int count = filteredOrders.size();
        tvOrdersCount.setText(count + (count == 1 ? " order" : " orders"));
    }

    private void updateEmptyState() {
        if (filteredOrders.isEmpty()) {
            layoutEmptyState.setVisibility(View.VISIBLE);
            rvOrders.setVisibility(View.GONE);
        } else {
            layoutEmptyState.setVisibility(View.GONE);
            rvOrders.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onViewDetails(Order order) {
        showOrderDetailsDialog(order);
    }

    @Override
    public void onUpdateStatus(Order order) {
        // Simple status progression: pending -> preparing -> ready -> finished
        String currentStatus = order.getStatus();
        String newStatus;
        
        switch (currentStatus.toLowerCase()) {
            case "pending":
                newStatus = "preparing";
                break;
            case "preparing":
                newStatus = "ready";
                break;
            case "ready":
                newStatus = "finished";
                break;
            default:
                Toast.makeText(this, "Order already finished", Toast.LENGTH_SHORT).show();
                return;
        }
        
        // Update order status via API first, fallback to MockDataManager
        apiService.updateOrderStatus(order.getOrderId(), newStatus, new ApiService.OrderActionCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                Toast.makeText(BaristaDashboardActivity.this, "Order #" + order.getOrderId() + " updated to " + newStatus, Toast.LENGTH_SHORT).show();
                loadOrders(); // Refresh the dashboard
            }

            @Override
            public void onError(String error) {
                // Fallback to MockDataManager
                boolean success = dataManager.updateOrderStatus(order.getOrderId(), newStatus);
                if (success) {
                    Toast.makeText(BaristaDashboardActivity.this, "Order #" + order.getOrderId() + " updated to " + newStatus + " (offline)", Toast.LENGTH_SHORT).show();
                    loadOrders(); // Refresh the dashboard
                } else {
                    Toast.makeText(BaristaDashboardActivity.this, "Failed to update status", Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    private void showOrderDetailsDialog(Order order) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Order #" + order.getOrderId() + " Details");
        
        StringBuilder details = new StringBuilder();
        details.append("Customer: ").append(order.getCustomerName()).append("\n");
        details.append("Status: ").append(order.getStatus().toUpperCase()).append("\n");
        details.append("Order Time: ").append(order.getFormattedCreatedAt()).append("\n");
        details.append("Total: ").append(order.getFormattedTotalPrice()).append("\n\n");
        details.append("Items:\n");
        
        if (order.getOrderItems() != null) {
            for (OrderItem item : order.getOrderItems()) {
                details.append("• ").append(item.getDisplayText()).append("\n");
            }
        }
        
        builder.setMessage(details.toString());
        builder.setPositiveButton("Close", null);
        builder.show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }        return super.onOptionsItemSelected(item);
    }
    
    private void setupBottomNavigation() {
        if (bottomNavigation == null) return;

        bottomNavigation.setSelectedItemId(R.id.nav_dashboard);
        bottomNavigation.setOnItemSelectedListener(new NavigationBarView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_dashboard) {
                    // Already on dashboard
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    // Navigate to Order Queue
                    Intent intent = new Intent(BaristaDashboardActivity.this, BaristaOrderQueueActivity.class);
                    startActivity(intent);
                    return true;
                } else if (itemId == R.id.nav_recipes) {
                    // Navigate to Recipe Guide
                    Intent intent = new Intent(BaristaDashboardActivity.this, BaristaRecipeGuideActivity.class);
                    startActivity(intent);
                    return true;
                } else if (itemId == R.id.nav_inventory) {
                    // Navigate to Inventory Check
                    Intent intent = new Intent(BaristaDashboardActivity.this, BaristaInventoryActivity.class);
                    startActivity(intent);
                    return true;
                } else if (itemId == R.id.nav_ready) {
                    // Navigate to Ready Orders
                    Intent intent = new Intent(BaristaDashboardActivity.this, BaristaReadyOrdersActivity.class);
                    startActivity(intent);
                    return true;
                }
                return false;
            }
        });
    }
}
