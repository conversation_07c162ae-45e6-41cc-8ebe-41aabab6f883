<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="?android:colorBackground">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="💰 Earnings Summary"
            android:textAppearance="?attr/textAppearanceHeadline2"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/tv_total_earnings"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textSize="20sp"
            android:textColor="@color/primary"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/tv_earnings_breakdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceBody1" />

    </LinearLayout>
</ScrollView>
