<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="4dp"
            app:title="Quick Actions"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Quick Stats -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Today's Overview"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/tv_orders_today"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="42"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="@color/primary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Orders Today"
                                android:textSize="10sp"
                                android:textColor="@color/text_secondary" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/tv_tasks_completed"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="6/8"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="@android:color/holo_green_dark" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Tasks Done"
                                android:textSize="10sp"
                                android:textColor="@color/text_secondary" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/tv_low_stock_items"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="3"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="@android:color/holo_orange_dark" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Low Stock"
                                android:textSize="10sp"
                                android:textColor="@color/text_secondary" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Primary Actions -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Quick Actions"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="16dp" />

            <!-- Action Grid -->
            <GridLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:columnCount="2"
                android:rowCount="3"
                android:layout_marginBottom="24dp">

                <!-- View Orders -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_view_orders"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@drawable/ic_list"
                            android:contentDescription="View Orders" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="View Orders"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginTop="8dp"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Check Inventory -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_check_inventory"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@drawable/ic_inventory"
                            android:contentDescription="Check Inventory" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Check Inventory"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginTop="8dp"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Daily Tasks -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_daily_tasks"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@drawable/ic_help"
                            android:contentDescription="Daily Tasks" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Daily Tasks"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginTop="8dp"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Ready Orders -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_ready_orders"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@drawable/ic_check"
                            android:contentDescription="Ready Orders" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Ready Orders"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginTop="8dp"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </GridLayout>

            <!-- Emergency Actions -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Emergency Actions"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="16dp" />

            <!-- Emergency Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_call_manager"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Call Manager"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:padding="16dp"
                app:backgroundTint="@android:color/holo_red_dark"
                app:cornerRadius="8dp"
                app:icon="@drawable/ic_help"
                app:iconGravity="start"
                android:layout_marginBottom="16dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/surface"
        app:itemIconTint="@color/primary"
        app:itemTextColor="@color/primary"
        app:menu="@menu/barista_bottom_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
