package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.Role;
import com.example.coffeeshop.models.User;
import com.example.coffeeshop.utils.TestUsers;

public class LoginActivity extends AppCompatActivity {

    private EditText etEmail, etPassword;
    private Button btnLogin;
    private TextView tvRegister, tvTestUsers;
    private MockDataManager dataManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        initViews();
        dataManager = MockDataManager.getInstance();
        setupClickListeners();
    }

    private void initViews() {
        etEmail = findViewById(R.id.et_email);
        etPassword = findViewById(R.id.et_password);
        btnLogin = findViewById(R.id.btn_login);
        tvRegister = findViewById(R.id.tv_register);
        tvTestUsers = findViewById(R.id.tv_test_users);
    }

    private void setupClickListeners() {
        btnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                performLogin();
            }
        });

        tvRegister.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(LoginActivity.this, RegisterActivity.class);
                startActivity(intent);
            }
        });

        tvTestUsers.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTestUsersDialog();
            }
        });
    }

    private void showTestUsersDialog() {
        new AlertDialog.Builder(this)
                .setTitle("Test User Credentials")
                .setMessage(TestUsers.getTestCredentials())
                .setPositiveButton("OK", null)
                .show();
    }

    private void performLogin() {
        String email = etEmail.getText().toString().trim();
        String password = etPassword.getText().toString().trim();

        if (email.isEmpty()) {
            etEmail.setError("Email is required");
            etEmail.requestFocus();
            return;
        }

        if (password.isEmpty()) {
            etPassword.setError("Password is required");
            etPassword.requestFocus();
            return;
        }

        User user = dataManager.loginUser(email, password);
        if (user != null) {
            Toast.makeText(this, "Login successful! Welcome " + user.getFullName(), Toast.LENGTH_SHORT).show();
            navigateToRoleActivity(user.getRoleId());
        } else {
            Toast.makeText(this, "Invalid email or password", Toast.LENGTH_SHORT).show();
        }
    }    private void navigateToRoleActivity(int roleId) {
        Intent intent;
        switch (roleId) {
            case Role.CUSTOMER:
                intent = new Intent(this, CustomerDashboardActivity.class);
                break;
            case Role.BARISTA:
                intent = new Intent(this, BaristaDashboardActivity.class);
                break;
            case Role.SHIPPER:
                intent = new Intent(this, ShipperDashboardActivity.class);
                break;
            case Role.MANAGER:
                intent = new Intent(this, ManagerDashboardActivity.class);
                break;
            case Role.CUSTOMER_SUPPORT:
                intent = new Intent(this, CustomerSupportDashboardActivity.class);
                break;
            default:
                intent = new Intent(this, CustomerDashboardActivity.class);
                break;
        }
        startActivity(intent);
        finish(); // Close login activity
    }
}
