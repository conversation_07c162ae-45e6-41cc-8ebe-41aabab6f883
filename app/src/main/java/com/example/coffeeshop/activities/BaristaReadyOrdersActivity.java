package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.ReadyOrderAdapter;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.Order;
import java.util.List;
import java.util.stream.Collectors;

public class BaristaReadyOrdersActivity extends AppCompatActivity {
    private RecyclerView rvReadyOrders;
    private ReadyOrderAdapter readyOrderAdapter;
    private SwipeRefreshLayout swipeRefresh;
    private LinearLayout layoutEmptyState;
    private TextView tvReadyCount, tvDeliveredToday;
    
    private List<Order> readyOrders;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_barista_ready_orders);

        initViews();
        setupBottomNavigation();
        setupRecyclerView();
        loadReadyOrders();
    }

    private void initViews() {
        rvReadyOrders = findViewById(R.id.rv_ready_orders);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        layoutEmptyState = findViewById(R.id.layout_empty_state);
        tvReadyCount = findViewById(R.id.tv_ready_count);
        tvDeliveredToday = findViewById(R.id.tv_delivered_today);

        swipeRefresh.setOnRefreshListener(this::loadReadyOrders);
    }

    private void setupBottomNavigation() {
        BottomNavigationView bottomNav = findViewById(R.id.bottom_navigation);
        bottomNav.setSelectedItemId(R.id.nav_ready);
        bottomNav.setOnNavigationItemSelectedListener(new BottomNavigationView.OnNavigationItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_dashboard) {
                    startActivity(new Intent(BaristaReadyOrdersActivity.this, BaristaDashboardActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    startActivity(new Intent(BaristaReadyOrdersActivity.this, BaristaOrderQueueActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_recipes) {
                    startActivity(new Intent(BaristaReadyOrdersActivity.this, BaristaRecipeGuideActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_inventory) {
                    startActivity(new Intent(BaristaReadyOrdersActivity.this, BaristaInventoryActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_ready) {
                    return true;
                }
                return false;
            }
        });
    }

    private void setupRecyclerView() {
        readyOrderAdapter = new ReadyOrderAdapter(this::onOrderDelivered);
        rvReadyOrders.setLayoutManager(new LinearLayoutManager(this));
        rvReadyOrders.setAdapter(readyOrderAdapter);
    }

    private void loadReadyOrders() {
        List<Order> allOrders = MockDataManager.getInstance().getOrders();
        readyOrders = allOrders.stream()
            .filter(order -> "ready".equals(order.getStatus()))
            .collect(Collectors.toList());
        
        updateSummary();
        updateUI();
        swipeRefresh.setRefreshing(false);
    }

    private void updateSummary() {
        int readyCount = readyOrders.size();
        int deliveredToday = MockDataManager.getInstance().getDeliveredOrdersToday();
        
        tvReadyCount.setText(String.valueOf(readyCount));
        tvDeliveredToday.setText(String.valueOf(deliveredToday));
    }

    private void updateUI() {
        if (readyOrders.isEmpty()) {
            layoutEmptyState.setVisibility(View.VISIBLE);
            rvReadyOrders.setVisibility(View.GONE);
        } else {
            layoutEmptyState.setVisibility(View.GONE);
            rvReadyOrders.setVisibility(View.VISIBLE);
            readyOrderAdapter.updateOrders(readyOrders);
        }
    }

    private void onOrderDelivered(Order order) {
        MockDataManager.getInstance().markOrderAsDelivered(order.getId());
        loadReadyOrders(); // Refresh the list
    }
}
