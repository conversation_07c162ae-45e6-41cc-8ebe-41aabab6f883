<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elevation="8dp"
            android:layout_marginBottom="16dp"
            android:backgroundTint="#FAFAFA"
            app:cardCornerRadius="18dp"
            app:cardElevation="8dp">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Refund / Service Credit" android:textStyle="bold" android:textSize="20sp" android:layout_gravity="center_horizontal" android:layout_marginBottom="16dp"/>

                <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Related Order ID" android:textStyle="bold"/>
                <Spinner android:id="@+id/spinnerOrderId" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginBottom="12dp"/>

                <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Transaction Type" android:textStyle="bold"/>
                <RadioGroup android:id="@+id/radioGroupType" android:layout_width="match_parent" android:layout_height="wrap_content" android:orientation="horizontal" android:layout_marginBottom="12dp">
                    <RadioButton android:id="@+id/radioRefund" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_weight="1" android:text="Refund"/>
                    <RadioButton android:id="@+id/radioCredit" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_weight="1" android:text="Service Credit"/>
                </RadioGroup>

                <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Amount" android:textStyle="bold"/>
                <EditText android:id="@+id/editAmount" android:layout_width="match_parent" android:layout_height="wrap_content" android:inputType="numberDecimal" android:hint="Enter amount" android:layout_marginBottom="12dp"/>

                <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Reason" android:textStyle="bold"/>
                <EditText android:id="@+id/editReason" android:layout_width="match_parent" android:layout_height="80dp" android:gravity="top|start" android:inputType="textMultiLine" android:hint="Enter reason (max 500 chars)" android:maxLength="500" android:layout_marginBottom="12dp"/>

                <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Original Payment Method" android:textStyle="bold"/>
                <Spinner android:id="@+id/spinnerPaymentMethod" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginBottom="12dp"/>

                <CheckBox android:id="@+id/checkboxConfirm" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="I confirm the refund/credit details are accurate." android:layout_marginBottom="16dp"/>

                <LinearLayout android:layout_width="match_parent" android:layout_height="wrap_content" android:orientation="horizontal">
                    <Button android:id="@+id/btnProcess" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_weight="1" android:text="Process"/>
                    <Button android:id="@+id/btnCancel" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_weight="1" android:text="Cancel" android:layout_marginStart="12dp"/>
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</ScrollView>
