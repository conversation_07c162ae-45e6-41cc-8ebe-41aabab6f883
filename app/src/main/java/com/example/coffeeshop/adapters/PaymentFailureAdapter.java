package com.example.coffeeshop.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.coffeeshop.R;
import com.example.coffeeshop.models.PaymentFailure;
import java.util.List;

public class PaymentFailureAdapter extends RecyclerView.Adapter<PaymentFailureAdapter.PaymentFailureViewHolder> {
    public interface OnResolveClickListener {
        void onResolve(PaymentFailure failure);
    }
    private List<PaymentFailure> failures;
    private OnResolveClickListener listener;

    public PaymentFailureAdapter(List<PaymentFailure> failures, OnResolveClickListener listener) {
        this.failures = failures;
        this.listener = listener;
    }

    @NonNull
    @Override
    public PaymentFailureViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_payment_failure, parent, false);
        return new PaymentFailureViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PaymentFailureViewHolder holder, int position) {
        PaymentFailure pf = failures.get(position);
        holder.tvReason.setText("Reason: " + pf.reason);
        holder.tvStatus.setText("Status: " + pf.status);
        holder.tvDate.setText("Date: " + pf.createdAt);
        holder.btnResolve.setOnClickListener(v -> {
            if (listener != null) listener.onResolve(pf);
        });
    }

    @Override
    public int getItemCount() {
        return failures.size();
    }

    public static class PaymentFailureViewHolder extends RecyclerView.ViewHolder {
        TextView tvReason, tvStatus, tvDate;
        Button btnResolve;
        public PaymentFailureViewHolder(@NonNull View itemView) {
            super(itemView);
            tvReason = itemView.findViewById(R.id.tv_failure_reason);
            tvStatus = itemView.findViewById(R.id.tv_failure_status);
            tvDate = itemView.findViewById(R.id.tv_failure_date);
            btnResolve = itemView.findViewById(R.id.btn_resolve_failure);
        }
    }
}
