<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Checkbox -->
        <CheckBox
            android:id="@+id/cb_task_completed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical" />

        <!-- Task Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_task_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Clean coffee machine"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/tv_task_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Daily maintenance and cleaning of the espresso machine"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/tv_task_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="15 minutes"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Priority Badge -->
        <TextView
            android:id="@+id/tv_priority"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="High"
            android:textSize="10sp"
            android:textColor="@android:color/white"
            android:background="@android:color/holo_red_dark"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:layout_gravity="top"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
