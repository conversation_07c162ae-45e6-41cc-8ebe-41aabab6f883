<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="?android:colorBackground">

    <!-- Tiêu đề -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="📞 Contact Customer"
        android:textAppearance="?attr/textAppearanceHeadline2"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Thông tin khách hàng -->
    <TextView
        android:id="@+id/tv_customer_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Customer: John Doe\nPhone: 0987654321"
        android:textAppearance="?attr/textAppearanceBody1"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Nút gọi điện -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_call"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="📲 Call Customer"
        style="@style/Widget.CoffeeShop.Button"
        app:layout_constraintTop_toBottomOf="@id/tv_customer_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="24dp"/>

    <!-- Nút gửi tin nhắn -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="💬 Send Message"
        style="@style/Widget.CoffeeShop.Button"
        app:layout_constraintTop_toBottomOf="@id/btn_call"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>
