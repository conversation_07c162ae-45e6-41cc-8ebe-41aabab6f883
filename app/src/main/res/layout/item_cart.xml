<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/Widget.CoffeeShop.ListItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp">

        <ImageView
            android:id="@+id/iv_product"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:contentDescription="Product image"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_launcher_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_product_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:text="Caramel Macchiato"
            android:textAppearance="?attr/textAppearanceSubtitle1"
            app:layout_constraintEnd_toStartOf="@+id/tv_product_price"
            app:layout_constraintStart_toEndOf="@+id/iv_product"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_product_details"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="Medium • Almond Milk"
            android:textAppearance="?attr/textAppearanceBody2"
            app:layout_constraintEnd_toEndOf="@+id/tv_product_name"
            app:layout_constraintStart_toStartOf="@+id/tv_product_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_product_name" />

        <TextView
            android:id="@+id/tv_product_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="$4.75"
            android:textAppearance="?attr/textAppearanceBody1"
            android:textColor="@color/secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tv_product_details"
            app:layout_constraintTop_toBottomOf="@+id/tv_product_details">

            <ImageButton
                android:id="@+id/btn_decrease"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Decrease quantity"
                android:src="@android:drawable/ic_menu_delete"
                app:tint="@color/primary" />

            <TextView
                android:id="@+id/tv_quantity"
                android:layout_width="36dp"
                android:layout_height="wrap_content"
                android:text="1"
                android:textAlignment="center"
                android:textAppearance="?attr/textAppearanceSubtitle1" />

            <ImageButton
                android:id="@+id/btn_increase"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Increase quantity"
                android:src="@android:drawable/ic_input_add"
                app:tint="@color/primary" />
        </LinearLayout>

        <ImageButton
            android:id="@+id/btn_remove"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Remove item"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            app:tint="@color/error"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
