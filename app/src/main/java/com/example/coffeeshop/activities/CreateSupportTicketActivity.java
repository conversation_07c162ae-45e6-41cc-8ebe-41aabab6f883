package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.widget.Button;
import android.widget.EditText;
import androidx.appcompat.app.AppCompatActivity;
import com.example.coffeeshop.data.MockDataManagerSupport;
import com.example.coffeeshop.models.SupportTicket;
import com.example.coffeeshop.R;
import java.util.ArrayList;

public class CreateSupportTicketActivity extends AppCompatActivity {
    private EditText editSubject, editDescription;
    private Button btnCreateTicket;
    private int customerId;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_create_support_ticket);
        customerId = getIntent().getIntExtra("customerId", 1);
        editSubject = findViewById(R.id.editSubject);
        editDescription = findViewById(R.id.editDescription);
        btnCreateTicket = findViewById(R.id.btnCreateTicket);
        btnCreateTicket.setOnClickListener(v -> {
            String subject = editSubject.getText().toString();
            String desc = editDescription.getText().toString();
            if (!subject.isEmpty() && !desc.isEmpty()) {
                SupportTicket ticket = new SupportTicket(0, customerId, "Test Customer", subject, desc, "Open", "Medium", new ArrayList<>(), 0, "2025-07-01 12:00", "2025-07-01 12:00");
                // For mock, just add to the list
                MockDataManagerSupport.getInstance().getTicketsForCustomer(customerId).add(ticket);
                finish();
            }
        });
    }
}
