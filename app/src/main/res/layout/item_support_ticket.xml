<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="10dp"
    android:elevation="2dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">
        <TextView
            android:id="@+id/textTicketName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ticket Name"
            android:textStyle="bold"
            android:textSize="16sp"/>
        <TextView
            android:id="@+id/textCustomer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Customer: <PERSON>"
            android:textSize="14sp"/>
        <TextView
            android:id="@+id/textTicketId"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ticket ID: 123"
            android:textSize="14sp"/>
        <TextView
            android:id="@+id/textPriority"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Priority: High"
            android:textSize="14sp"/>
        <TextView
            android:id="@+id/textStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Status: Open"
            android:textSize="14sp"/>
        <TextView
            android:id="@+id/textAssignedStaff"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Assigned to: "
            android:textStyle="italic"
            android:textColor="@android:color/holo_blue_dark"
            android:visibility="gone"
            android:textSize="14sp"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">
            <Button
                android:id="@+id/btnViewDetail"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="View Detail"/>
            <Button
                android:id="@+id/btnAssignStaff"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Assign to Staff"
                android:layout_marginStart="8dp"/>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
