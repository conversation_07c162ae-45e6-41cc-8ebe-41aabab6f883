package com.example.coffeeshop.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.coffeeshop.R;
import com.example.coffeeshop.data.MockOrderHistoryProvider;
import java.util.List;

public class OrderHistoryCardAdapter extends RecyclerView.Adapter<OrderHistoryCardAdapter.OrderViewHolder> {
    public interface OnOrderActionListener {
        void onViewOrderDetails(MockOrderHistoryProvider.OrderHistoryItem order);
    }
    private List<MockOrderHistoryProvider.OrderHistoryItem> orders;
    private OnOrderActionListener listener;

    public OrderHistoryCardAdapter(List<MockOrderHistoryProvider.OrderHistoryItem> orders, OnOrderActionListener listener) {
        this.orders = orders;
        this.listener = listener;
    }

    @NonNull
    @Override
    public OrderViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_order_history, parent, false);
        return new OrderViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull OrderViewHolder holder, int position) {
        MockOrderHistoryProvider.OrderHistoryItem order = orders.get(position);
        holder.tvOrderId.setText("Order ID: " + order.orderId);
        holder.tvOrderDate.setText("Date: " + order.date);
        holder.tvOrderTotal.setText("Total: $" + order.total);
        holder.tvOrderStatus.setText("Status: " + order.status);
        holder.tvOrderItems.setText("Items: " + android.text.TextUtils.join(", ", order.items));
        holder.btnViewDetails.setOnClickListener(v -> {
            if (listener != null) listener.onViewOrderDetails(order);
        });
    }

    @Override
    public int getItemCount() {
        return orders.size();
    }

    public static class OrderViewHolder extends RecyclerView.ViewHolder {
        TextView tvOrderId, tvOrderDate, tvOrderTotal, tvOrderStatus, tvOrderItems;
        Button btnViewDetails;
        public OrderViewHolder(@NonNull View itemView) {
            super(itemView);
            tvOrderId = itemView.findViewById(R.id.tv_order_id);
            tvOrderDate = itemView.findViewById(R.id.tv_order_date);
            tvOrderTotal = itemView.findViewById(R.id.tv_order_total);
            tvOrderStatus = itemView.findViewById(R.id.tv_order_status);
            tvOrderItems = itemView.findViewById(R.id.tv_order_items);
            btnViewDetails = itemView.findViewById(R.id.btn_view_details);
        }
    }
}
