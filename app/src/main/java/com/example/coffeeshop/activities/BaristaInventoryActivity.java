package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import android.widget.TextView;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.InventoryAdapter;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.InventoryItem;
import org.json.JSONArray;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class BaristaInventoryActivity extends AppCompatActivity {
    private RecyclerView rvInventory;
    private InventoryAdapter inventoryAdapter;
    private SwipeRefreshLayout swipeRefresh;
    private ChipGroup chipGroupFilter;
    private TextView tvTotalItems, tvLowStockCount, tvOutOfStockCount;
    
    private List<InventoryItem> allItems;
    private String currentFilter = "all";
    private ApiService apiService;
    private MockDataManager mockDataManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_barista_inventory);

        apiService = new ApiService();
        mockDataManager = MockDataManager.getInstance();
        allItems = new ArrayList<>();
        
        initViews();
        setupBottomNavigation();
        setupRecyclerView();
        setupFilters();
        loadInventoryData();
    }

    private void initViews() {
        rvInventory = findViewById(R.id.rv_inventory);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        chipGroupFilter = findViewById(R.id.chip_group_filter);
        tvTotalItems = findViewById(R.id.tv_total_items);
        tvLowStockCount = findViewById(R.id.tv_low_stock_count);
        tvOutOfStockCount = findViewById(R.id.tv_out_of_stock_count);

        swipeRefresh.setOnRefreshListener(this::loadInventoryData);
    }

    private void setupBottomNavigation() {
        BottomNavigationView bottomNav = findViewById(R.id.bottom_navigation);
        bottomNav.setSelectedItemId(R.id.nav_inventory);
        bottomNav.setOnNavigationItemSelectedListener(new BottomNavigationView.OnNavigationItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_dashboard) {
                    startActivity(new Intent(BaristaInventoryActivity.this, BaristaDashboardActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    startActivity(new Intent(BaristaInventoryActivity.this, BaristaOrderQueueActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_recipes) {
                    startActivity(new Intent(BaristaInventoryActivity.this, BaristaRecipeGuideActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_inventory) {
                    return true;
                } else if (itemId == R.id.nav_ready) {
                    startActivity(new Intent(BaristaInventoryActivity.this, BaristaReadyOrdersActivity.class));
                    finish();
                    return true;
                }
                return false;
            }
        });
    }

    private void setupRecyclerView() {
        inventoryAdapter = new InventoryAdapter();
        rvInventory.setLayoutManager(new LinearLayoutManager(this));
        rvInventory.setAdapter(inventoryAdapter);
    }

    private void setupFilters() {
        chipGroupFilter.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.chip_all) {
                currentFilter = "all";
            } else if (checkedId == R.id.chip_low_stock) {
                currentFilter = "low_stock";
            } else if (checkedId == R.id.chip_out_of_stock) {
                currentFilter = "out_of_stock";
            }
            filterInventory();
        });
    }

    private void loadInventoryData() {
        // Load based on current filter to optimize API calls
        switch (currentFilter) {
            case "low_stock":
                loadLowStockItems();
                break;
            case "out_of_stock":
                loadOutOfStockItems();
                break;
            default:
                loadAllInventoryItems();
                break;
        }
    }
    
    private void loadAllInventoryItems() {
        apiService.getInventoryItems(new ApiService.InventoryCallback() {
            @Override
            public void onSuccess(JSONArray inventoryArray) {
                try {
                    allItems = parseInventoryItems(inventoryArray);
                    updateSummary();
                    filterInventory();
                    swipeRefresh.setRefreshing(false);
                } catch (Exception e) {
                    e.printStackTrace();
                    loadInventoryFromMock();
                }
            }

            @Override
            public void onError(String error) {
                loadInventoryFromMock();
            }
        });
    }
    
    private void loadLowStockItems() {
        apiService.getLowStockItems(new ApiService.InventoryCallback() {
            @Override
            public void onSuccess(JSONArray inventoryArray) {
                try {
                    List<InventoryItem> lowStockItems = parseInventoryItems(inventoryArray);
                    
                    // For summary, we still need all items, so load them separately
                    loadAllInventoryForSummary();
                    
                    // Display filtered items
                    inventoryAdapter.updateItems(lowStockItems);
                    swipeRefresh.setRefreshing(false);
                } catch (Exception e) {
                    e.printStackTrace();
                    loadInventoryFromMock();
                }
            }

            @Override
            public void onError(String error) {
                loadInventoryFromMock();
            }
        });
    }
    
    private void loadOutOfStockItems() {
        apiService.getOutOfStockItems(new ApiService.InventoryCallback() {
            @Override
            public void onSuccess(JSONArray inventoryArray) {
                try {
                    List<InventoryItem> outOfStockItems = parseInventoryItems(inventoryArray);
                    
                    // For summary, we still need all items, so load them separately
                    loadAllInventoryForSummary();
                    
                    // Display filtered items
                    inventoryAdapter.updateItems(outOfStockItems);
                    swipeRefresh.setRefreshing(false);
                } catch (Exception e) {
                    e.printStackTrace();
                    loadInventoryFromMock();
                }
            }

            @Override
            public void onError(String error) {
                loadInventoryFromMock();
            }
        });
    }
    
    private void loadAllInventoryForSummary() {
        apiService.getInventoryItems(new ApiService.InventoryCallback() {
            @Override
            public void onSuccess(JSONArray inventoryArray) {
                try {
                    allItems = parseInventoryItems(inventoryArray);
                    updateSummary();
                } catch (Exception e) {
                    // Summary will be updated from mock data
                }
            }

            @Override
            public void onError(String error) {
                // Summary will be updated from mock data
            }
        });
    }
    
    private void loadInventoryFromMock() {
        allItems = mockDataManager.getInventoryItems();
        updateSummary();
        filterInventory();
        swipeRefresh.setRefreshing(false);
    }
    
    private List<InventoryItem> parseInventoryItems(JSONArray inventoryArray) throws Exception {
        List<InventoryItem> items = new ArrayList<>();
        
        for (int i = 0; i < inventoryArray.length(); i++) {
            JSONObject itemJson = inventoryArray.getJSONObject(i);
            
            InventoryItem item = new InventoryItem(
                itemJson.getInt("item_id"),
                itemJson.getString("name"),
                itemJson.getInt("quantity"),
                itemJson.optInt("reorder_level", 5), // default to 5 if not provided
                itemJson.getString("unit")
            );
            
            items.add(item);
        }
        
        return items;
    }

    private void filterInventory() {
        List<InventoryItem> filteredItems;
        
        switch (currentFilter) {
            case "low_stock":
                filteredItems = allItems.stream()
                    .filter(item -> item.getQuantity() > 0 && item.getQuantity() <= item.getMinStock())
                    .collect(Collectors.toList());
                break;
            case "out_of_stock":
                filteredItems = allItems.stream()
                    .filter(item -> item.getQuantity() == 0)
                    .collect(Collectors.toList());
                break;
            default:
                filteredItems = allItems;
                break;
        }
        
        inventoryAdapter.updateItems(filteredItems);
    }

    private void updateSummary() {
        int totalItems = allItems.size();
        int lowStockCount = (int) allItems.stream()
            .filter(item -> item.getQuantity() > 0 && item.getQuantity() <= item.getMinStock())
            .count();
        int outOfStockCount = (int) allItems.stream()
            .filter(item -> item.getQuantity() == 0)
            .count();

        tvTotalItems.setText(String.valueOf(totalItems));
        tvLowStockCount.setText(String.valueOf(lowStockCount));
        tvOutOfStockCount.setText(String.valueOf(outOfStockCount));
    }
}
