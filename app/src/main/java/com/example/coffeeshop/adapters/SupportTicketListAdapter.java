package com.example.coffeeshop.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.coffeeshop.models.SupportTicket;
import com.example.coffeeshop.R;
import java.util.List;

public class SupportTicketListAdapter extends RecyclerView.Adapter<SupportTicketListAdapter.TicketViewHolder> {
    public interface OnTicketActionListener {
        void onViewDetail(SupportTicket ticket);
        void onAssignStaff(SupportTicket ticket);
    }
    private List<SupportTicket> tickets;
    private OnTicketActionListener actionListener;

    public SupportTicketListAdapter(List<SupportTicket> tickets, OnTicketActionListener actionListener) {
        this.tickets = tickets;
        this.actionListener = actionListener;
    }

    @NonNull
    @Override
    public TicketViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_support_ticket, parent, false);
        return new TicketViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TicketViewHolder holder, int position) {
        SupportTicket ticket = tickets.get(position);
        holder.textTicketName.setText(ticket.subject);
        holder.textCustomer.setText("Customer: " + ticket.customerName);
        holder.textTicketId.setText("Ticket ID: " + ticket.id);
        holder.textPriority.setText("Priority: " + ticket.priority);
        holder.textStatus.setText("Status: " + ticket.status);
        holder.textAssignedStaff.setVisibility(ticket.assignedStaffName != null ? View.VISIBLE : View.GONE);
        if (ticket.assignedStaffName != null) {
            holder.textAssignedStaff.setText("Assigned to: " + ticket.assignedStaffName);
        }
        holder.btnViewDetail.setOnClickListener(v -> actionListener.onViewDetail(ticket));
        holder.btnAssignStaff.setOnClickListener(v -> actionListener.onAssignStaff(ticket));
    }

    @Override
    public int getItemCount() {
        return tickets.size();
    }

    static class TicketViewHolder extends RecyclerView.ViewHolder {
        TextView textTicketName, textCustomer, textTicketId, textPriority, textStatus, textAssignedStaff;
        View btnViewDetail, btnAssignStaff;
        TicketViewHolder(@NonNull View itemView) {
            super(itemView);
            textTicketName = itemView.findViewById(R.id.textTicketName);
            textCustomer = itemView.findViewById(R.id.textCustomer);
            textTicketId = itemView.findViewById(R.id.textTicketId);
            textPriority = itemView.findViewById(R.id.textPriority);
            textStatus = itemView.findViewById(R.id.textStatus);
            textAssignedStaff = itemView.findViewById(R.id.textAssignedStaff);
            btnViewDetail = itemView.findViewById(R.id.btnViewDetail);
            btnAssignStaff = itemView.findViewById(R.id.btnAssignStaff);
        }
    }
}
