package com.example.coffeeshop.activities;


import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;
import com.google.android.material.button.MaterialButton;

public class ShipperContactCustomerActivity extends AppCompatActivity {

    private String customerPhone = "0987654321";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_contact_customer);

        MaterialButton btnCall = findViewById(R.id.btn_call);
        MaterialButton btnMessage = findViewById(R.id.btn_message);

        btnCall.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent callIntent = new Intent(Intent.ACTION_DIAL);
                callIntent.setData(Uri.parse("tel:" + customerPhone));
                startActivity(callIntent);
            }
        });

        btnMessage.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent smsIntent = new Intent(Intent.ACTION_VIEW);
                smsIntent.setData(Uri.parse("smsto:" + customerPhone));
                smsIntent.putExtra("sms_body", "Hello, I'm contacting you about your order.");
                startActivity(smsIntent);
            }
        });
    }
}
