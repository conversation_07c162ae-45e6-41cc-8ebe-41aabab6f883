<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="4dp"
            app:title="Daily Tasks"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Progress Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Today's Progress"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="12dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/tv_completed_tasks"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:textColor="@android:color/holo_green_dark" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Completed"
                                    android:textSize="12sp"
                                    android:textColor="@color/text_secondary" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/tv_pending_tasks"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:textColor="@android:color/holo_orange_dark" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Pending"
                                    android:textSize="12sp"
                                    android:textColor="@color/text_secondary" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- Progress Bar -->
                        <com.google.android.material.progressindicator.LinearProgressIndicator
                            android:id="@+id/progress_bar"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            app:indicatorColor="@android:color/holo_green_dark" />

                        <TextView
                            android:id="@+id/tv_progress_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="0% Complete"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary"
                            android:gravity="center"
                            android:layout_marginTop="8dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Tasks Filter -->
                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chip_group_filter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:singleSelection="true">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_all_tasks"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="All Tasks"
                        android:checked="true"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_pending"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Pending"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_completed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Completed"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                </com.google.android.material.chip.ChipGroup>

                <!-- Tasks Header -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Daily Tasks"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <!-- Tasks RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_tasks"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    tools:listitem="@layout/item_daily_task" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/surface"
        app:itemIconTint="@color/primary"
        app:itemTextColor="@color/primary"
        app:menu="@menu/barista_bottom_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
