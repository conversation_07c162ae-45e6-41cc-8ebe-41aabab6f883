package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.coffeeshop.adapters.SupportChatAdapter;
import com.example.coffeeshop.data.MockDataManagerSupport;
import com.example.coffeeshop.models.SupportTicket;
import com.example.coffeeshop.models.SupportResponse;
import com.example.coffeeshop.R;

public class SupportTicketDetailActivity extends AppCompatActivity {
    private TextView textSubject, textDescription, textStatus;
    private RecyclerView recyclerViewChat;
    private SupportChatAdapter chatAdapter;
    private EditText editChatMessage;
    private Button btnSendMessage;
    private Button btnEscalate, btnUpdateStatus, btnOrderHistory, btnRefund, btnPaymentFailure;
    private int ticketId;
    private SupportTicket ticket;
    private int currentUserId = 2; // Example: 2 for staff, 1 for customer

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_support_ticket_detail);
        ticketId = getIntent().getIntExtra("ticketId", -1);
        ticket = MockDataManagerSupport.getInstance().getTicketById(ticketId);
        textSubject = findViewById(R.id.textSubject);
        textDescription = findViewById(R.id.textDescription);
        textStatus = findViewById(R.id.textStatus);
        recyclerViewChat = findViewById(R.id.recyclerViewChat);
        editChatMessage = findViewById(R.id.editChatMessage);
        btnSendMessage = findViewById(R.id.btnSendMessage);
        btnEscalate = findViewById(R.id.btnEscalate);
        btnUpdateStatus = findViewById(R.id.btnUpdateStatus);
        btnOrderHistory = findViewById(R.id.btnOrderHistory);
        btnRefund = findViewById(R.id.btnRefund);
        btnPaymentFailure = findViewById(R.id.btnPaymentFailure);
        recyclerViewChat.setLayoutManager(new LinearLayoutManager(this));

        if (ticket != null) {
            chatAdapter = new SupportChatAdapter(ticket.responses, currentUserId);
            recyclerViewChat.setAdapter(chatAdapter);
            textSubject.setText(ticket.subject);
            textDescription.setText(ticket.description);
            textStatus.setText(ticket.status);
            btnSendMessage.setOnClickListener(v -> {
                String msg = editChatMessage.getText().toString();
                if (!msg.isEmpty()) {
                    String role = currentUserId == ticket.customerId ? "Customer" : "Staff";
                    SupportResponse response = new SupportResponse(0, ticketId, currentUserId, role, msg, "2025-07-01 12:00");
                    MockDataManagerSupport.getInstance().addResponseToTicket(ticketId, response);
                    chatAdapter.notifyItemInserted(ticket.responses.size() - 1);
                    recyclerViewChat.scrollToPosition(ticket.responses.size() - 1);
                    editChatMessage.setText("");
                }
            });
            btnEscalate.setOnClickListener(v -> {
                Intent intent = new Intent(this, EscalateTicketActivity.class);
                intent.putExtra("assigneeName", ticket != null ? ticket.assignedStaffName : "Staff Member");
                startActivity(intent);
            });
            btnUpdateStatus.setOnClickListener(v -> MockDataManagerSupport.getInstance().updateTicketStatus(ticketId, "Resolved"));
            btnOrderHistory.setOnClickListener(v -> {
                Intent intent = new Intent(this, OrderHistoryForTicketActivity.class);
                intent.putExtra("customerId", ticket.customerId);
                startActivity(intent);
            });
            btnRefund.setOnClickListener(v -> {
                Intent intent = new Intent(this, RefundOrCreditActivity.class);
                intent.putExtra("orderId", ticket.relatedOrderId);
                startActivity(intent);
            });
            btnPaymentFailure.setOnClickListener(v -> {
                Intent intent = new Intent(this, PaymentFailureActivity.class);
                intent.putExtra("customerId", ticket.customerId);
                startActivity(intent);
            });
        } else {
            // Ticket not found, disable buttons and show error
            textSubject.setText("Ticket not found");
            textDescription.setText("");
            textStatus.setText("");
            btnSendMessage.setEnabled(false);
            btnEscalate.setEnabled(false);
            btnUpdateStatus.setEnabled(false);
            btnOrderHistory.setEnabled(false);
            btnRefund.setEnabled(false);
            btnPaymentFailure.setEnabled(false);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Refresh ticket status in case it was updated in detail
        if (textStatus != null && ticket != null) {
            textStatus.setText(ticket.status);
        }
    }
}
