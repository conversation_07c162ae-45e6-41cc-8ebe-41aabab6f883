package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.ReadyOrderAdapter;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.Order;
import org.json.JSONArray;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

public class BaristaReadyOrdersActivity extends AppCompatActivity {
    private RecyclerView rvReadyOrders;
    private ReadyOrderAdapter readyOrderAdapter;
    private SwipeRefreshLayout swipeRefresh;
    private LinearLayout layoutEmptyState;
    private TextView tvReadyCount, tvDeliveredToday;
    
    private List<Order> readyOrders;
    private ApiService apiService;
    private MockDataManager mockDataManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_barista_ready_orders);

        apiService = new ApiService();
        mockDataManager = MockDataManager.getInstance();
        readyOrders = new ArrayList<>();
        
        initViews();
        setupBottomNavigation();
        setupRecyclerView();
        loadReadyOrders();
    }

    private void initViews() {
        rvReadyOrders = findViewById(R.id.rv_ready_orders);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        layoutEmptyState = findViewById(R.id.layout_empty_state);
        tvReadyCount = findViewById(R.id.tv_ready_count);
        tvDeliveredToday = findViewById(R.id.tv_delivered_today);

        swipeRefresh.setOnRefreshListener(this::loadReadyOrders);
    }

    private void setupBottomNavigation() {
        BottomNavigationView bottomNav = findViewById(R.id.bottom_navigation);
        bottomNav.setSelectedItemId(R.id.nav_ready);
        bottomNav.setOnNavigationItemSelectedListener(new BottomNavigationView.OnNavigationItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_dashboard) {
                    startActivity(new Intent(BaristaReadyOrdersActivity.this, BaristaDashboardActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    startActivity(new Intent(BaristaReadyOrdersActivity.this, BaristaOrderQueueActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_recipes) {
                    startActivity(new Intent(BaristaReadyOrdersActivity.this, BaristaRecipeGuideActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_inventory) {
                    startActivity(new Intent(BaristaReadyOrdersActivity.this, BaristaInventoryActivity.class));
                    finish();
                    return true;
                } else if (itemId == R.id.nav_ready) {
                    return true;
                }
                return false;
            }
        });
    }

    private void setupRecyclerView() {
        readyOrderAdapter = new ReadyOrderAdapter(this::onOrderDelivered);
        rvReadyOrders.setLayoutManager(new LinearLayoutManager(this));
        rvReadyOrders.setAdapter(readyOrderAdapter);
    }

    private void loadReadyOrders() {
        // Load ready orders from API first, fallback to MockDataManager
        apiService.getReadyOrders(new ApiService.OrdersCallback() {
            @Override
            public void onSuccess(JSONArray ordersArray) {
                try {
                    readyOrders = parseOrders(ordersArray);
                    updateSummary();
                    updateUI();
                    swipeRefresh.setRefreshing(false);
                } catch (Exception e) {
                    e.printStackTrace();
                    loadReadyOrdersFromMock();
                }
            }

            @Override
            public void onError(String error) {
                loadReadyOrdersFromMock();
            }
        });
    }
    
    private void loadReadyOrdersFromMock() {
        List<Order> allOrders = MockDataManager.getInstance().getOrders();
        readyOrders = allOrders.stream()
            .filter(order -> "ready".equals(order.getStatus()))
            .collect(Collectors.toList());
        
        updateSummary();
        updateUI();
        swipeRefresh.setRefreshing(false);
    }
    
    private List<Order> parseOrders(JSONArray ordersArray) throws Exception {
        List<Order> orders = new ArrayList<>();
        
        for (int i = 0; i < ordersArray.length(); i++) {
            JSONObject orderJson = ordersArray.getJSONObject(i);
            
            // Parse date from API format
            Date orderDate;
            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                String dateField = orderJson.has("order_date") ? "order_date" : "created_at";
                orderDate = dateFormat.parse(orderJson.getString(dateField));
            } catch (Exception e) {
                orderDate = new Date(); // Fallback to current date
            }
            
            Order order = new Order(
                orderJson.getInt("order_id"),
                orderJson.getInt("customer_id"),
                orderJson.getString("customer_name"),
                orderJson.getString("status"),
                new BigDecimal(orderJson.has("total_amount") ? orderJson.getDouble("total_amount") : orderJson.getDouble("total_price")),
                null, // discount
                orderDate
            );
            
            // Set additional fields from API response
            if (orderJson.has("items_summary")) {
                order.setItemsSummary(orderJson.getString("items_summary"));
            }
            
            orders.add(order);
        }
        
        return orders;
    }

    private void updateSummary() {
        int readyCount = readyOrders.size();
        tvReadyCount.setText(String.valueOf(readyCount));
        
        // Get delivered orders count from API
        apiService.getOrdersByStatus("delivered", new ApiService.OrdersCallback() {
            @Override
            public void onSuccess(JSONArray ordersArray) {
                // Filter today's delivered orders
                int deliveredToday = 0;
                try {
                    String today = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(new Date());
                    for (int i = 0; i < ordersArray.length(); i++) {
                        JSONObject orderJson = ordersArray.getJSONObject(i);
                        String createdAt = orderJson.getString("created_at");
                        if (createdAt.startsWith(today)) {
                            deliveredToday++;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                
                final int finalDeliveredToday = deliveredToday;
                runOnUiThread(() -> tvDeliveredToday.setText(String.valueOf(finalDeliveredToday)));
            }

            @Override
            public void onError(String error) {
                // Fallback to MockDataManager
                int deliveredToday = MockDataManager.getInstance().getDeliveredOrdersToday();
                tvDeliveredToday.setText(String.valueOf(deliveredToday));
            }
        });
    }

    private void updateUI() {
        if (readyOrders.isEmpty()) {
            layoutEmptyState.setVisibility(View.VISIBLE);
            rvReadyOrders.setVisibility(View.GONE);
        } else {
            layoutEmptyState.setVisibility(View.GONE);
            rvReadyOrders.setVisibility(View.VISIBLE);
            readyOrderAdapter.updateOrders(readyOrders);
        }
    }

    private void onOrderDelivered(Order order) {
        // Update order status to delivered via API first, fallback to MockDataManager
        apiService.updateOrderStatus(order.getOrderId(), "delivered", new ApiService.OrderActionCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                Toast.makeText(BaristaReadyOrdersActivity.this, "Order #" + order.getOrderId() + " marked as delivered", Toast.LENGTH_SHORT).show();
                loadReadyOrders(); // Refresh the list
            }

            @Override
            public void onError(String error) {
                // Fallback to MockDataManager
                MockDataManager.getInstance().markOrderAsDelivered(order.getId());
                Toast.makeText(BaristaReadyOrdersActivity.this, "Order #" + order.getOrderId() + " marked as delivered (offline)", Toast.LENGTH_SHORT).show();
                loadReadyOrders(); // Refresh the list
            }
        });
    }
}
