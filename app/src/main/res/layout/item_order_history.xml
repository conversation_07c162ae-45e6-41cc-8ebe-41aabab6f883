<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/item_order_history"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row: Order ID + Status -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_order_id"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Order ID"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333" />

            <TextView
                android:id="@+id/tv_order_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="STATUS"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#4CAF50"
                android:background="@drawable/status_badge"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp" />

        </LinearLayout>

        <!-- Row: Order Date + Total -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_order_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Order Date"
                android:textSize="14sp"
                android:textColor="#666" />

            <TextView
                android:id="@+id/tv_order_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Order Total"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#8B4513" />

        </LinearLayout>

        <!-- Items Summary -->
        <TextView
            android:id="@+id/tv_order_items"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Items Summary"
            android:textSize="14sp"
            android:textColor="#666"
            android:layout_marginBottom="12dp" />

        <!-- Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_view_details"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="View Details"
                android:textSize="14sp"
                android:textColor="#8B4513"
                android:background="@drawable/button_outline"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_reorder"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="Reorder"
                android:textSize="14sp"
                android:textColor="@android:color/white"
                android:background="@drawable/button_primary" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
