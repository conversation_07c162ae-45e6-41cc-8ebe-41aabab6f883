package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.RecipeAdapter;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.Recipe;
import com.example.coffeeshop.models.RecipeStep;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationBarView;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BaristaRecipeGuideActivity extends AppCompatActivity {

    private Spinner spinnerCategory;
    private RecyclerView rvRecipes;
    private BottomNavigationView bottomNavigation;

    private MockDataManager dataManager;
    private ApiService apiService;
    private RecipeAdapter recipeAdapter;
    private List<Recipe> allRecipes;
    private List<Recipe> filteredRecipes;
    private List<String> categories;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_barista_recipe_guide);

        initializeViews();
        setupData();
        setupRecyclerView();
        setupCategoryFilter();
        setupBottomNavigation();
        loadRecipes();
    }

    private void initializeViews() {
        spinnerCategory = findViewById(R.id.spinner_category);
        rvRecipes = findViewById(R.id.rv_recipes);
        bottomNavigation = findViewById(R.id.bottom_navigation);
    }

    private void setupData() {
        dataManager = MockDataManager.getInstance();
        apiService = new ApiService();
        allRecipes = new ArrayList<>();
        filteredRecipes = new ArrayList<>();
        categories = new ArrayList<>();
        categories.add("All"); // Default category
    }

    private void setupRecyclerView() {
        recipeAdapter = new RecipeAdapter(filteredRecipes, this::onRecipeClick);
        rvRecipes.setLayoutManager(new LinearLayoutManager(this));
        rvRecipes.setAdapter(recipeAdapter);
    }

    private void setupCategoryFilter() {
        // Load categories from API first
        loadRecipeCategories();
    }
    
    private void loadRecipeCategories() {
        apiService.getRecipeCategories(new ApiService.CategoriesCallback() {
            @Override
            public void onSuccess(JSONArray categoriesArray) {
                try {
                    categories.clear();
                    categories.add("All"); // Always add "All" option
                    
                    for (int i = 0; i < categoriesArray.length(); i++) {
                        categories.add(categoriesArray.getString(i));
                    }
                    
                    setupCategorySpinner();
                } catch (Exception e) {
                    e.printStackTrace();
                    setupCategorySpinnerWithDefaults();
                }
            }

            @Override
            public void onError(String error) {
                setupCategorySpinnerWithDefaults();
            }
        });
    }
    
    private void setupCategorySpinner() {
        ArrayAdapter<String> categoryAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, categories);
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCategory.setAdapter(categoryAdapter);

        spinnerCategory.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                filterRecipesByCategory(categories.get(position));
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // Do nothing
            }
        });
    }
    
    private void setupCategorySpinnerWithDefaults() {
        // Fallback to default categories
        categories.clear();
        categories.add("All");
        categories.add("easy");
        categories.add("medium");
        categories.add("hard");
        setupCategorySpinner();
    }

    private void setupBottomNavigation() {
        bottomNavigation.setSelectedItemId(R.id.nav_recipes);
        bottomNavigation.setOnItemSelectedListener(new NavigationBarView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_dashboard) {
                    startActivity(new Intent(BaristaRecipeGuideActivity.this, BaristaDashboardActivity.class));
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    startActivity(new Intent(BaristaRecipeGuideActivity.this, BaristaOrderQueueActivity.class));
                    return true;
                } else if (itemId == R.id.nav_recipes) {
                    return true; // Current activity
                } else if (itemId == R.id.nav_inventory) {
                    startActivity(new Intent(BaristaRecipeGuideActivity.this, BaristaInventoryActivity.class));
                    return true;
                } else if (itemId == R.id.nav_ready) {
                    startActivity(new Intent(BaristaRecipeGuideActivity.this, BaristaReadyOrdersActivity.class));
                    return true;
                }
                return false;
            }
        });
    }

    private void loadRecipes() {
        apiService.getRecipes(new ApiService.RecipesCallback() {
            @Override
            public void onSuccess(JSONArray recipesArray) {
                try {
                    allRecipes = parseRecipes(recipesArray);
                    filteredRecipes.clear();
                    filteredRecipes.addAll(allRecipes);
                    recipeAdapter.notifyDataSetChanged();
                } catch (Exception e) {
                    e.printStackTrace();
                    loadRecipesFromMock();
                }
            }

            @Override
            public void onError(String error) {
                loadRecipesFromMock();
            }
        });
    }
    
    private void loadRecipesFromMock() {
        allRecipes = dataManager.getRecipes();
        filteredRecipes.clear();
        filteredRecipes.addAll(allRecipes);
        recipeAdapter.notifyDataSetChanged();
    }
    
    private List<Recipe> parseRecipes(JSONArray recipesArray) throws Exception {
        List<Recipe> recipes = new ArrayList<>();
        
        for (int i = 0; i < recipesArray.length(); i++) {
            JSONObject recipeJson = recipesArray.getJSONObject(i);
            
            // Parse recipe steps
            List<RecipeStep> steps = new ArrayList<>();
            if (recipeJson.has("steps")) {
                JSONArray stepsArray = recipeJson.getJSONArray("steps");
                for (int j = 0; j < stepsArray.length(); j++) {
                    JSONObject stepJson = stepsArray.getJSONObject(j);
                    RecipeStep step = new RecipeStep(
                        j + 1, // step number
                        recipeJson.getInt("recipe_id"),
                        stepJson.getString("description"),
                        stepJson.getInt("time_required")
                    );
                    steps.add(step);
                }
            }
            
            Recipe recipe = new Recipe(
                recipeJson.getInt("recipe_id"),
                recipeJson.getString("name"),
                recipeJson.getString("description"),
                new Date(), // creation date
                steps
            );
            
            // Set additional fields if available
            if (recipeJson.has("difficulty_level")) {
                recipe.setCategory(recipeJson.getString("difficulty_level"));
            }
            // Note: Recipe model doesn't have setEstimatedTime method, 
            // preparation time is handled internally
            
            recipes.add(recipe);
        }
        
        return recipes;
    }

    private void filterRecipesByCategory(String category) {
        if ("All".equals(category)) {
            // Load all recipes
            loadRecipes();
        } else {
            // Load recipes by specific category
            apiService.getRecipesByCategory(category, new ApiService.RecipesCallback() {
                @Override
                public void onSuccess(JSONArray recipesArray) {
                    try {
                        List<Recipe> categoryRecipes = parseRecipes(recipesArray);
                        filteredRecipes.clear();
                        filteredRecipes.addAll(categoryRecipes);
                        recipeAdapter.notifyDataSetChanged();
                    } catch (Exception e) {
                        e.printStackTrace();
                        filterRecipesFromMock(category);
                    }
                }

                @Override
                public void onError(String error) {
                    filterRecipesFromMock(category);
                }
            });
        }
    }
    
    private void filterRecipesFromMock(String category) {
        filteredRecipes.clear();
        
        if ("All".equals(category)) {
            filteredRecipes.addAll(allRecipes);
        } else {
            for (Recipe recipe : allRecipes) {
                if (category.equals(recipe.getCategory())) {
                    filteredRecipes.add(recipe);
                }
            }
        }
        
        recipeAdapter.notifyDataSetChanged();
    }

    private void onRecipeClick(Recipe recipe) {
        Intent intent = new Intent(this, RecipeDetailActivity.class);
        intent.putExtra("recipe_id", recipe.getId());
        startActivity(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        bottomNavigation.setSelectedItemId(R.id.nav_recipes);
    }
}
