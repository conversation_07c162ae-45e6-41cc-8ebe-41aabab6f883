package com.example.coffeeshop.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.models.CartItem;

import java.util.List;

public class CartAdapter extends RecyclerView.Adapter<CartAdapter.CartViewHolder> {

    private List<CartItem> cartItems;
    private Context context;
    private OnCartItemActionListener actionListener;

    public interface OnCartItemActionListener {
        void onQuantityChanged(CartItem cartItem, int newQuantity);
        void onRemoveItem(CartItem cartItem);
    }

    public CartAdapter(Context context, List<CartItem> cartItems) {
        this.context = context;
        this.cartItems = cartItems;
    }

    public void setOnCartItemActionListener(OnCartItemActionListener listener) {
        this.actionListener = listener;
    }

    public void updateCartItems(List<CartItem> newCartItems) {
        this.cartItems = newCartItems;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public CartViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_cart, parent, false);
        return new CartViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CartViewHolder holder, int position) {
        CartItem cartItem = cartItems.get(position);
        holder.bind(cartItem);
    }

    @Override
    public int getItemCount() {
        return cartItems.size();
    }

    class CartViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivProduct;
        private TextView tvProductName;
        private TextView tvProductDetails;
        private TextView tvProductPrice;
        private TextView tvQuantity;
        private ImageButton btnDecrease, btnIncrease, btnRemove;

        public CartViewHolder(@NonNull View itemView) {
            super(itemView);
            ivProduct = itemView.findViewById(R.id.iv_product);
            tvProductName = itemView.findViewById(R.id.tv_product_name);
            tvProductDetails = itemView.findViewById(R.id.tv_product_details);
            tvProductPrice = itemView.findViewById(R.id.tv_product_price);
            tvQuantity = itemView.findViewById(R.id.tv_quantity);
            btnDecrease = itemView.findViewById(R.id.btn_decrease);
            btnIncrease = itemView.findViewById(R.id.btn_increase);
            btnRemove = itemView.findViewById(R.id.btn_remove);
        }

        public void bind(CartItem cartItem) {
            tvProductName.setText(cartItem.getProduct().getName());
            tvProductDetails.setText(cartItem.getProduct().getCategory());
            tvProductPrice.setText(String.format("$%.2f", cartItem.getTotalPrice()));
            tvQuantity.setText(String.valueOf(cartItem.getQuantity()));

            // For now, use placeholder image
            ivProduct.setImageResource(R.drawable.ic_launcher_background);

            btnDecrease.setOnClickListener(v -> {
                if (actionListener != null) {
                    int newQuantity = cartItem.getQuantity() - 1;
                    actionListener.onQuantityChanged(cartItem, newQuantity);
                }
            });

            btnIncrease.setOnClickListener(v -> {
                if (actionListener != null) {
                    int newQuantity = cartItem.getQuantity() + 1;
                    actionListener.onQuantityChanged(cartItem, newQuantity);
                }
            });

            btnRemove.setOnClickListener(v -> {
                if (actionListener != null) {
                    actionListener.onRemoveItem(cartItem);
                }
            });

            // Disable decrease button if quantity is 1
            btnDecrease.setEnabled(cartItem.getQuantity() > 1);
        }
    }
}
