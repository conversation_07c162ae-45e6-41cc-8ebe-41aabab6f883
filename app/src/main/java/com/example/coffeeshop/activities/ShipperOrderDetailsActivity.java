package com.example.coffeeshop.activities;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;
import com.google.android.material.button.MaterialButton;

public class ShipperOrderDetailsActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_order_details);

        String orderId = getIntent().getStringExtra("orderId");
        String customer = getIntent().getStringExtra("customer");
        String address = getIntent().getStringExtra("address");
        String status = getIntent().getStringExtra("status");

        String info = "Order ID: " + orderId + "\nCustomer: " + customer + "\nAddress: " + address + "\nStatus: ";

        SpannableString spannable = new SpannableString(info + status);
        int start = info.length();
        int end = start + status.length();

        int color;
        if ("Accepted".equalsIgnoreCase(status)) {
            color = Color.parseColor("#008000");
        } else if ("Rejected".equalsIgnoreCase(status)) {
            color = Color.parseColor("#8B0000");
        } else {
            color = Color.BLACK;
        }

        spannable.setSpan(new ForegroundColorSpan(color), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        TextView tvInfo = findViewById(R.id.tv_order_info);
        tvInfo.setText(spannable);

        // Xử lý nút chuyển sang Update Status
        MaterialButton btnUpdate = findViewById(R.id.btn_update_status);
        btnUpdate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(ShipperOrderDetailsActivity.this, ShipperUpdateOrderStatusActivity.class);
                intent.putExtra("orderId", orderId); // truyền thêm nếu cần
                startActivity(intent);
            }
        });

        MaterialButton btnContact = findViewById(R.id.btn_contact_customer);
        btnContact.setOnClickListener(v -> {
            Intent intent = new Intent(this, ShipperContactCustomerActivity.class);
            startActivity(intent);
        });

        MaterialButton btnViewMap = findViewById(R.id.btn_view_map);
        btnViewMap.setOnClickListener(v -> {
            Intent intent = new Intent(ShipperOrderDetailsActivity.this, ShipperViewMapActivity.class);
            startActivity(intent);
        });


    }
}