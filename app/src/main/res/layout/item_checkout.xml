<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:gravity="center_vertical">

    <ImageView
        android:id="@+id/iv_product_image"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:scaleType="centerCrop"
        android:background="@drawable/circle_background"
        android:layout_marginEnd="12dp" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_product_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Product Name"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333"
            android:maxLines="2"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/tv_unit_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="$4.50 each"
            android:textSize="14sp"
            android:textColor="#666"
            android:layout_marginTop="2dp" />

        <TextView
            android:id="@+id/tv_quantity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
<<<<<<< Updated upstream
            android:text="Qty: 2"
            android:textSize="14sp"
            android:textColor="#8B4513"
            android:layout_marginTop="2dp" />
=======
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="4dp">

            <TextView
                android:id="@+id/tv_quantity_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Qty: "
                android:textSize="14sp"
                android:textColor="#8B4513" />

            <Button
                android:id="@+id/btn_decrease_quantity"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:text="-"
                android:textSize="16sp"
                android:background="@drawable/circle_background"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="4dp" />

            <TextView
                android:id="@+id/tv_quantity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2"
                android:textSize="14sp"
                android:textColor="#8B4513"
                android:minWidth="24dp"
                android:gravity="center"
                android:layout_marginHorizontal="8dp" />

            <Button
                android:id="@+id/btn_increase_quantity"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:text="+"
                android:textSize="16sp"
                android:background="@drawable/circle_background"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="8dp" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <ImageButton
                android:id="@+id/btn_remove_item"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_delete"
                android:background="@drawable/circle_background"
                android:contentDescription="Remove item"
                app:tint="#FF6B6B" />

        </LinearLayout>
>>>>>>> Stashed changes

    </LinearLayout>

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="$9.00"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#8B4513" />

</LinearLayout>
