{"name": "coffee-shop-backend", "version": "1.0.0", "description": "Backend API for Coffee Shop Management System", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "init-db": "node src/db/init.js"}, "keywords": ["coffee", "shop", "management", "system", "api"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^2.0.22"}}