<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:background="@color/surface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Customer Name and Date -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_customer_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="<PERSON>"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/tv_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Dec 15, 2024"
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

        <!-- Rating -->
        <TextView
            android:id="@+id/tv_rating"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="★★★★★"
            android:textSize="16sp"
            android:textColor="@color/warning"
            android:layout_marginBottom="8dp" />

        <!-- Comment -->
        <TextView
            android:id="@+id/tv_comment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Great coffee! Perfect strength and amazing taste."
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:lineHeight="20dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
