<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="?android:colorBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal">

        <!-- Tiêu đề -->
        <TextView
            android:id="@+id/tv_status_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🚚 Update Order Status"
            android:textAppearance="?attr/textAppearanceHeadline2"
            android:layout_marginBottom="24dp" />

        <!-- Spinner chọn trạng thái -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Select new status:"
            android:textAppearance="?attr/textAppearanceBody1"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/spinner_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/primary"
            android:minHeight="48dp" />

        <!-- Nút xác nhận -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_confirm_update"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Confirm Update"
            android:layout_marginTop="24dp"
            style="@style/Widget.CoffeeShop.Button" />

    </LinearLayout>
</ScrollView>
