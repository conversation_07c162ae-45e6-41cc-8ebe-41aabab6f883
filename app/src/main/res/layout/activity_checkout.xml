<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f5f5f5">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        android:clipToPadding="false"
        android:paddingBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="20dp">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_back"
                android:contentDescription="Back" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Checkout"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="#333"
                android:gravity="center" />

            <View
                android:layout_width="40dp"
                android:layout_height="40dp" />

        </LinearLayout>

        <!-- Order Items Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Order Items"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#333"
                    android:layout_marginBottom="12dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_checkout_items"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Delivery Address Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Delivery Address"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#333"
                    android:layout_marginBottom="12dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Enter delivery address"
                    app:startIconDrawable="@drawable/ic_location"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_delivery_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:lines="3" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:hint="Special instructions (optional)"
                    app:startIconDrawable="@drawable/ic_note"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_special_instructions"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:lines="2" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Payment Method Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Payment Method"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#333"
                    android:layout_marginBottom="12dp" />

                <RadioGroup
                    android:id="@+id/rg_payment_method"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <RadioButton
                        android:id="@+id/rb_cod"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Cash on Delivery"
                        android:textSize="16sp"
                        android:padding="12dp"
                        android:checked="true"
                        android:drawableStart="@drawable/ic_money"
                        android:drawablePadding="12dp" />

                    <RadioButton
                        android:id="@+id/rb_qr_payment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="QR Payment"
                        android:textSize="16sp"
                        android:padding="12dp"
                        android:drawableStart="@drawable/ic_qr_code"
                        android:drawablePadding="12dp" />

                </RadioGroup>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Order Summary Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Order Summary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#333"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Subtotal"
                        android:textSize="16sp"
                        android:textColor="#666" />

                    <TextView
                        android:id="@+id/tv_subtotal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="$0.00"
                        android:textSize="16sp"
                        android:textColor="#333" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Delivery Fee"
                        android:textSize="16sp"
                        android:textColor="#666" />

                    <TextView
                        android:id="@+id/tv_delivery_fee"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="$2.50"
                        android:textSize="16sp"
                        android:textColor="#333" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#e0e0e0"
                    android:layout_marginVertical="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Total"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#333" />

                    <TextView
                        android:id="@+id/tv_total"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="$0.00"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#8B4513" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Place Order Button -->
        <Button
            android:id="@+id/btn_place_order"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="Place Order"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:background="@drawable/button_primary"
            android:layout_marginBottom="20dp" />

    </LinearLayout>

    </ScrollView>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/surface"
        app:itemIconTint="@color/primary"
        app:itemTextColor="@color/primary"
        app:menu="@menu/customer_bottom_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
