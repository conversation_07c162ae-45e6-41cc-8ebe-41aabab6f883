package com.example.coffeeshop.activities;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.CheckoutItemAdapter;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.CartItem;
import com.example.coffeeshop.models.Order;
import com.example.coffeeshop.models.OrderItem;
import com.example.coffeeshop.models.Product;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationBarView;
import com.google.android.material.textfield.TextInputEditText;

import org.json.JSONArray;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CheckoutActivity extends AppCompatActivity implements CheckoutItemAdapter.OnCartItemActionListener {

    private RecyclerView rvCheckoutItems;
    private TextView tvSubtotal, tvDeliveryFee, tvTotal;
    private TextInputEditText etDeliveryAddress, etSpecialInstructions;
    private RadioGroup rgPaymentMethod;
    private Button btnPlaceOrder;
    private BottomNavigationView bottomNavigation;
    
    private MockDataManager dataManager;
    private ApiService apiService;
    private CheckoutItemAdapter adapter;
    private List<CartItem> cartItems;
    private BigDecimal subtotal = BigDecimal.ZERO;
    private BigDecimal deliveryFee = new BigDecimal("2.50");
    private int currentUserId = 1; // Default customer

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_checkout);
        
        // Get user ID from intent
        currentUserId = getIntent().getIntExtra("userId", 1);
        
        dataManager = MockDataManager.getInstance();
        apiService = new ApiService();
        initViews();
        setupBottomNavigation();
        loadCartItems();
        setupListeners();
    }

    private void initViews() {
        rvCheckoutItems = findViewById(R.id.rv_checkout_items);
        tvSubtotal = findViewById(R.id.tv_subtotal);
        tvDeliveryFee = findViewById(R.id.tv_delivery_fee);
        tvTotal = findViewById(R.id.tv_total);
        etDeliveryAddress = findViewById(R.id.et_delivery_address);
        etSpecialInstructions = findViewById(R.id.et_special_instructions);
        rgPaymentMethod = findViewById(R.id.rg_payment_method);
        btnPlaceOrder = findViewById(R.id.btn_place_order);
        bottomNavigation = findViewById(R.id.bottom_navigation);
    }

    private void loadCartItems() {
        // Initialize empty list first
        cartItems = new ArrayList<>();
        
        // Try API first, then fallback to MockDataManager
        apiService.getCartItems(currentUserId, new ApiService.CartCallback() {
            @Override
            public void onSuccess(JSONArray cartItemsJson) {
                try {
                    cartItems.clear();
                    for (int i = 0; i < cartItemsJson.length(); i++) {
                        JSONObject itemJson = cartItemsJson.getJSONObject(i);
                        
                        // Get product details for the cart item
                        int productId = itemJson.getInt("product_id");
                        int quantity = itemJson.getInt("quantity");
                        
                        // Find product from API or MockDataManager
                        Product product = dataManager.findProductById(productId);
                        if (product != null) {
                            CartItem cartItem = new CartItem(
                                itemJson.getInt("cart_id"),
                                currentUserId,
                                productId,
                                product,
                                quantity,
                                product.getPrice(),
                                new Date()
                            );
                            cartItems.add(cartItem);
                        }
                    }
                    
                    setupRecyclerView();
                    calculateTotals();
                    
                } catch (Exception e) {
                    e.printStackTrace();
                    loadCartItemsFromMock();
                }
            }

            @Override
            public void onError(String error) {
                loadCartItemsFromMock();
            }
        });
    }
    
    private void loadCartItemsFromMock() {
        cartItems = dataManager.getCartItemsByUserId(currentUserId);
        setupRecyclerView();
        calculateTotals();
    }

    private void setupRecyclerView() {
        adapter = new CheckoutItemAdapter(cartItems);
        adapter.setOnCartItemActionListener(this);
        rvCheckoutItems.setLayoutManager(new LinearLayoutManager(this));
        rvCheckoutItems.setAdapter(adapter);
    }

    private void calculateTotals() {
        subtotal = BigDecimal.ZERO;
        
        for (CartItem item : cartItems) {
            BigDecimal itemTotal = item.getUnitPrice().multiply(new BigDecimal(item.getQuantity()));
            subtotal = subtotal.add(itemTotal);
        }
        
        BigDecimal total = subtotal.add(deliveryFee);
        
        tvSubtotal.setText("$" + subtotal.setScale(2, BigDecimal.ROUND_HALF_UP));
        tvDeliveryFee.setText("$" + deliveryFee.setScale(2, BigDecimal.ROUND_HALF_UP));
        tvTotal.setText("$" + total.setScale(2, BigDecimal.ROUND_HALF_UP));
    }

    private void setupListeners() {
        btnPlaceOrder.setOnClickListener(v -> placeOrder());
        
        findViewById(R.id.btn_back).setOnClickListener(v -> finish());
    }

    private void placeOrder() {
        // Validate inputs
        String address = etDeliveryAddress.getText().toString().trim();
        if (address.isEmpty()) {
            etDeliveryAddress.setError("Delivery address is required");
            etDeliveryAddress.requestFocus();
            return;
        }
        
        if (cartItems.isEmpty()) {
            Toast.makeText(this, "Your cart is empty", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Get selected payment method
        int selectedPaymentId = rgPaymentMethod.getCheckedRadioButtonId();
        final String paymentMethod;
        if (selectedPaymentId == R.id.rb_qr_payment) {
            paymentMethod = "qr";
        } else {
            paymentMethod = "cash"; // Default - use lowercase for API
        }
        
        final String finalAddress = address;
        
        try {
            // Create order via API first, then fallback to MockDataManager
            BigDecimal totalAmount = subtotal.add(deliveryFee);
            final String specialInstructions = etSpecialInstructions.getText().toString().trim();
            final BigDecimal finalTotalAmount = totalAmount;
            
            // Get customer name from SharedPreferences
            SharedPreferences prefs = getSharedPreferences("user_data", MODE_PRIVATE);
            String customerName = prefs.getString("full_name", "Customer");
            
            // Prepare order items for API
            JSONArray orderItems = new JSONArray();
            for (CartItem cartItem : cartItems) {
                JSONObject item = new JSONObject();
                item.put("product_id", cartItem.getProductId());
                item.put("product_name", cartItem.getProduct() != null ? cartItem.getProduct().getName() : "Unknown Product");
                item.put("quantity", cartItem.getQuantity());
                item.put("unit_price", cartItem.getUnitPrice().doubleValue());
                orderItems.put(item);
            }
            
            // Place order via API
            apiService.createOrder(currentUserId, customerName, finalTotalAmount.doubleValue(), 
                finalAddress, specialInstructions, paymentMethod, orderItems, 
                new ApiService.OrderActionCallback() {
                    @Override
                    public void onSuccess(JSONObject result) {
                        try {
                            int orderId = result.getInt("order_id");
                            
                            // Clear cart via API
                            apiService.clearCart(currentUserId, new ApiService.CartActionCallback() {
                                @Override
                                public void onSuccess(String message) {
                                    Toast.makeText(CheckoutActivity.this, "Order placed successfully!", Toast.LENGTH_SHORT).show();
                                    
                                    // Navigate to order tracking
                                    Intent intent = new Intent(CheckoutActivity.this, OrderTrackingActivity.class);
                                    intent.putExtra("orderId", orderId);
                                    intent.putExtra("userId", currentUserId);
                                    startActivity(intent);
                                    finish();
                                }

                                @Override
                                public void onError(String error) {
                                    // Cart clearing failed, but order was placed successfully
                                    Toast.makeText(CheckoutActivity.this, "Order placed successfully!", Toast.LENGTH_SHORT).show();
                                    
                                    Intent intent = new Intent(CheckoutActivity.this, OrderTrackingActivity.class);
                                    intent.putExtra("orderId", orderId);
                                    intent.putExtra("userId", currentUserId);
                                    startActivity(intent);
                                    finish();
                                }
                            });
                            
                        } catch (Exception e) {
                            e.printStackTrace();
                            Toast.makeText(CheckoutActivity.this, "Order placed but failed to process response", Toast.LENGTH_SHORT).show();
                            finish();
                        }
                    }

                    @Override
                    public void onError(String error) {
                        // Fallback to MockDataManager
                        placeOrderWithMockData(finalAddress, paymentMethod, specialInstructions, finalTotalAmount);
                    }
                });
            
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Error placing order: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    
    private void placeOrderWithMockData(String address, String paymentMethod, String specialInstructions, BigDecimal totalAmount) {
        try {
            Order order = new Order(
                0, // Will be set by data manager
                currentUserId,
                "Customer", // Customer name - will be updated by data manager
                "pending",
                totalAmount,
                null, // No discount
                new Date()
            );
            
            // Initialize order items list
            order.setOrderItems(new ArrayList<>());
            
            // Add order items
            for (CartItem cartItem : cartItems) {
                Product product = dataManager.findProductById(cartItem.getProductId());
                String productName = (product != null) ? product.getName() : "Unknown Product";
                
                OrderItem orderItem = new OrderItem(
                    0, // Will be set by data manager
                    0, // Order ID will be set after order creation
                    cartItem.getProductId(),
                    productName,
                    cartItem.getQuantity(),
                    cartItem.getUnitPrice()
                );
                order.getOrderItems().add(orderItem);
            }
            
            // Save order and clear cart
            dataManager.addOrder(order);
            dataManager.clearCart(currentUserId);
            
            Toast.makeText(this, "Order placed successfully! (Offline)", Toast.LENGTH_SHORT).show();
            
            // Navigate to order tracking
            Intent intent = new Intent(this, OrderTrackingActivity.class);
            intent.putExtra("orderId", order.getOrderId());
            intent.putExtra("userId", currentUserId);
            startActivity(intent);
            finish();
            
        } catch (Exception e) {
            Toast.makeText(this, "Error placing order: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    
    private void setupBottomNavigation() {
        if (bottomNavigation == null) return;

        bottomNavigation.setSelectedItemId(R.id.nav_menu); // Checkout is part of menu flow
        bottomNavigation.setOnItemSelectedListener(new NavigationBarView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_home) {
                    // Navigate to home
                    Intent intent = new Intent(CheckoutActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_menu) {
                    // Navigate to menu (home)
                    Intent intent = new Intent(CheckoutActivity.this, CustomerDashboardActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_orders) {
                    // Navigate to Order History
                    Intent intent = new Intent(CheckoutActivity.this, OrderHistoryActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                } else if (itemId == R.id.nav_favorites) {
                    // Show a simple message since favorites isn't implemented
                    Toast.makeText(CheckoutActivity.this, "Favorites - Coming Soon!", Toast.LENGTH_SHORT).show();
                    return true;
                } else if (itemId == R.id.nav_profile) {
                    // Navigate to Profile
                    Intent intent = new Intent(CheckoutActivity.this, ProfileActivity.class);
                    intent.putExtra("userId", currentUserId);
                    startActivity(intent);
                    finish();
                    return true;
                }
                return false;
            }
        });
    }

    @Override
    public void onQuantityChanged(CartItem cartItem, int newQuantity) {
        // Update quantity via API first, then fallback to mock
        apiService.updateCartItem(cartItem.getCartItemId(), newQuantity, new ApiService.CartActionCallback() {
            @Override
            public void onSuccess(String message) {
                // Update local cart item
                cartItem.setQuantity(newQuantity);
                adapter.notifyDataSetChanged();
                calculateTotals();
                Toast.makeText(CheckoutActivity.this, "Quantity updated", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String error) {
                // Fallback to mock data manager
                boolean success = dataManager.updateCartItemQuantity(cartItem.getCartItemId(), newQuantity);
                if (success) {
                    cartItem.setQuantity(newQuantity);
                    adapter.notifyDataSetChanged();
                    calculateTotals();
                    Toast.makeText(CheckoutActivity.this, "Quantity updated (Offline)", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(CheckoutActivity.this, "Failed to update quantity", Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    @Override
    public void onItemRemoved(CartItem cartItem) {
        // Remove item via API first, then fallback to mock
        apiService.removeFromCart(cartItem.getCartItemId(), new ApiService.CartActionCallback() {
            @Override
            public void onSuccess(String message) {
                // Remove from local list
                cartItems.remove(cartItem);
                adapter.notifyDataSetChanged();
                calculateTotals();
                Toast.makeText(CheckoutActivity.this, "Item removed from cart", Toast.LENGTH_SHORT).show();
                
                // Check if cart is empty
                if (cartItems.isEmpty()) {
                    Toast.makeText(CheckoutActivity.this, "Cart is empty", Toast.LENGTH_SHORT).show();
                    finish(); // Close checkout activity
                }
            }

            @Override
            public void onError(String error) {
                // Fallback to mock data manager
                boolean success = dataManager.removeFromCart(cartItem.getCartItemId());
                if (success) {
                    cartItems.remove(cartItem);
                    adapter.notifyDataSetChanged();
                    calculateTotals();
                    Toast.makeText(CheckoutActivity.this, "Item removed from cart (Offline)", Toast.LENGTH_SHORT).show();
                    
                    if (cartItems.isEmpty()) {
                        Toast.makeText(CheckoutActivity.this, "Cart is empty", Toast.LENGTH_SHORT).show();
                        finish();
                    }
                } else {
                    Toast.makeText(CheckoutActivity.this, "Failed to remove item", Toast.LENGTH_SHORT).show();
                }
            }
        });
    }
}
