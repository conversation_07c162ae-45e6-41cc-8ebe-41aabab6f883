package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;

public class ShipperViewScheduleActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_view_schedule);

        TextView tvSchedule = findViewById(R.id.tv_schedule);

        // Dữ liệu lịch làm việc ảo
        String scheduleData =
                "📅 Monday\n08:00 - 12:00 (Morning Shift)\n14:00 - 18:00 (Afternoon Shift)\n\n" +
                        "📅 Tuesday\n09:00 - 17:00\n\n" +
                        "📅 Wednesday\nDay Off\n\n" +
                        "📅 Thursday\n08:00 - 12:00\n\n" +
                        "📅 Friday\n14:00 - 20:00\n\n" +
                        "📅 Saturday\n09:00 - 13:00\n\n" +
                        "📅 Sunday\nDay Off";

        tvSchedule.setText(scheduleData);
    }
}
